// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    // ext {
    //     buildToolsVersion = findProperty('android.buildToolsVersion') ?: '34.0.0'
    //     minSdkVersion = Integer.parseInt(findProperty('android.minSdkVersion') ?: '24')
    //     compileSdkVersion = Integer.parseInt(findProperty('android.compileSdkVersion') ?: '34')
    //     targetSdkVersion = Integer.parseInt(findProperty('android.targetSdkVersion') ?: '34')
    //     kotlinVersion = findProperty('android.kotlinVersion') ?: '1.9.0'
    //     kotlin_version = kotlinVersion
    //     frescoVersion = findProperty('expo.frescoVersion') ?: '2.5.0'
    //     FLIPPER_VERSION = '0.201.0'

    //     ndkVersion = "25.1.8937393"
    // }
    // repositories {
    //     google()
    //     mavenCentral()
    // }
    // dependencies {
    //     classpath('com.android.tools.build:gradle:8.5.0')
    //     classpath('com.facebook.react:react-native-gradle-plugin')
    //     classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion"
    //     classpath 'com.google.gms:google-services:4.3.15'
    //     classpath("com.facebook.react:react-native-gradle-plugin")
    //     classpath("io.sentry:sentry-android-gradle-plugin:4.13.0")
    // }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath('com.android.tools.build:gradle')
        classpath('com.facebook.react:react-native-gradle-plugin')
        classpath('org.jetbrains.kotlin:kotlin-gradle-plugin')

        classpath("com.google.gms:google-services:4.3.15")
        classpath("io.sentry:sentry-android-gradle-plugin:4.13.0")
    }
}

def reactNativeAndroidDir = new File(
  providers.exec {
    workingDir(rootDir)
    commandLine("node", "--print", "require.resolve('react-native/package.json')")
  }.standardOutput.asText.get().trim(),
  "../android"
)

allprojects {
    // repositories {
    //     maven {
    //         // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
    //         url(new File(['node', '--print', "require.resolve('react-native/package.json')"].execute(null, rootDir).text.trim(), '../android'))
    //     }
    //     maven {
    //         // Android JSC is installed from npm
    //         url(new File(['node', '--print', "require.resolve('jsc-android/package.json')"].execute(null, rootDir).text.trim(), '../dist'))
    //     }

        // maven {
        //     url "$rootDir/../../../node_modules/@notifee/react-native/android/libs"
        // }
        
    //     google()
    //     mavenCentral()
    //     maven { url 'https://www.jitpack.io' }
    //     maven { url 'https://maven.mozilla.org/maven2' }
    // }
    repositories {
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url(reactNativeAndroidDir)
        }
        maven {
            url "$rootDir/../../../node_modules/@notifee/react-native/android/libs"
        }
        google()
        mavenCentral()
        maven { url 'https://www.jitpack.io' }
    }
}

apply plugin: "expo-root-project"
apply plugin: "com.facebook.react.rootproject"

// @generated begin expo-camera-import - expo prebuild (DO NOT MODIFY) sync-f244f4f3d8bf7229102e8f992b525b8602c74770
def expoCameraMavenPath = new File(["node", "--print", "require.resolve('expo-camera/package.json')"].execute(null, rootDir).text.trim(), "../android/maven")
allprojects { repositories { maven { url(expoCameraMavenPath) } } }
// @generated end expo-camera-import

// fix issue:
// Execution failed for task ':bufgix-react-native-secure-window:compileDebugKotlin'.
// 'compileDebugJavaWithJavac' task (current target is 17) and 'compileDebugKotlin' task (current target is 11) jvm target compatibility should be set to the same Java version.
allprojects {
    project(':bufgix-react-native-secure-window') {
        tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
            kotlinOptions {
                jvmTarget = "17"
            }
        }
    }

    
    // fix issue:
    // Execution failed for task ':react-native-image-colors:compileDebugKotlin'.
    // Inconsistent JVM-target compatibility detected for tasks 'compileDebugJavaWithJavac' (17) and 'compileDebugKotlin' (18).
    project(':react-native-image-colors') {
        tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
            kotlinOptions {
                jvmTarget = "17"
            }
        }
    }
    
    // fix issue:
    // Execution failed for task ':react-native-passkeys:compileDebugKotlin'.
    // Inconsistent JVM-target compatibility detected for tasks 'compileDebugJavaWithJavac' (17) and 'compileDebugKotlin' (18).
    project(':react-native-passkeys') {
        tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
            kotlinOptions {
                jvmTarget = "17"
            }
        }
    }
    
}

name: release-android

on:
  workflow_run:
    workflows:
      - daily-build
      - daily-build-dev
    types:
      - completed
  workflow_dispatch:
    inputs:
      is-split-bundle:
        description: "Should it run as split-bundle? (keep it null if you don't need it)"
        required: false
        default: ''

jobs:
  release-android:
    runs-on: ubuntu-latest
    if: ${{ !github.event.workflow_run || (github.event.workflow_run && github.event.workflow_run.conclusion == 'success') }}
    steps:
      - name: Show executed time
        run: |
          echo "Executed at: $(date '+%Y-%m-%d %H:%M:%S')"

      - name: Checkout Source Code
        uses: actions/checkout@v3
        with:
          lfs: true

      - name: Run Shared Env Setup
        uses: ./.github/actions/shared-env
        with:
          additional_env: |
            NO_FLIPPER=1
            SENTRY_DISABLE_AUTO_UPLOAD=false
          env_file_name: '.env.expo'
          sentry_project: ''
          covalent_key: ${{ secrets.COVALENT_KEY }}
          sentry_token: ${{ secrets.SENTRY_TOKEN }}
          sentry_dsn_react_native: ${{ secrets.SENTRY_DSN_REACT_NATIVE }}
          sentry_dsn_web: ${{ secrets.SENTRY_DSN_WEB }}
          sentry_dsn_desktop: ${{ secrets.SENTRY_DSN_DESKTOP }}
          sentry_dsn_mas: ${{ secrets.SENTRY_DSN_MAS }}
          sentry_dsn_snap: ${{ secrets.SENTRY_DSN_SNAP }}
          sentry_dsn_winms: ${{ secrets.SENTRY_DSN_WINMS }}
          sentry_dsn_ext: ${{ secrets.SENTRY_DSN_EXT }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
          registry-url: 'https://registry.npmjs.org'
          always-auth: true
          scope: '@onekeyhq'

      - name: Setup keys secret
        run: |
          echo ${{ secrets.AUTO_SUBMIT_GOOGLE_PLAY_KEY }} | base64 -d > apps/mobile/pc-api-8644398719570171123-0-dcae07e2afab.json
          rm -rf apps/mobile/android/app/google-services.json
          echo ${{ secrets.GOOGLE_SERVICE_ANDROID }} | base64 -d > apps/mobile/android/app/google-services.json

      - name: Setup EAS
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Modify build.gradle
        uses: OneKeyHQ/actions/build-gradle-edit@main
        with:
          versionCode: ${{ env.BUILD_NUMBER }}
          versionName: ${{ env.BUILD_APP_VERSION }}
          packageName: 'so.onekey.app.wallet'
          android_dir: ./apps/mobile

      - name: Write .env.version
        run: |
          # pass BUILD_NUMBER as env variable to expo server
          echo "BUILD_NUMBER=${{ env.BUILD_NUMBER }}" >> .env.version

      - name: Write .sentry.properties
        run: |
          echo "auth.token=${{ secrets.SENTRY_TOKEN }}" >> apps/mobile/android/sentry.properties

      - name: Install Dep
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_OPTIONS: '--max_old_space_size=8192'
        run: |
          yarn

      - name: Build on EAS And Submit
        if: ${{ github.event.workflow_run }}
        run: |
          cd apps/mobile

          # if env.WORKFLOW_BUILD_NUMBER exists and start with 1000, it's a dev build
          if [[ -n "${{ env.WORKFLOW_BUILD_NUMBER }}" && "${{ env.WORKFLOW_BUILD_NUMBER }}" == 1000* ]]; then
            eas build --profile=production --platform android --non-interactive --no-wait --resource-class large
          else
            eas build --profile=production --platform android --non-interactive --no-wait --resource-class large
            eas build --profile=production-store --platform android --non-interactive --no-wait --resource-class large --auto-submit
            # eas build --profile=production-huawei --platform android --non-interactive --no-wait --resource-class large
          fi

      - name: Build on EAS
        if: ${{ !github.event.workflow_run }}
        run: |
          cd apps/mobile
          eas build --profile=production --platform android --non-interactive --no-wait --resource-class large

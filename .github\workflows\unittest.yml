name: 02-unittest

on:
  workflow_dispatch:
  pull_request:
    branches:
      - onekey
      - x
  push:
    branches:
      - onekey
      - x

# Cancel a currently running workflow from the same PR/branch/tag
# when a new workflow is triggered
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  unittest:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [20.x]

    steps:
      - uses: actions/checkout@v3
        with:
          lfs: true

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: 'https://npm.pkg.github.com'
          always-auth: true
          check-latest: true
          scope: '@onekeyhq'

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn config get cacheFolder)" >> $GITHUB_OUTPUT

      - name: Cache dependencies
        uses: actions/cache/restore@v4
        id: yarn-cache
        with:
          path: |
            ${{ steps.yarn-cache-dir-path.outputs.dir }}
            **/node_modules
          key: unittest-${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock', '**/patches/**') }}
          restore-keys: |
            unittest-${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock', '**/patches/**') }}

      - name: Install Dependency
        if: steps.yarn-cache.outputs.cache-hit != 'true'
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_OPTIONS: '--max_old_space_size=4096'
        run: |
          yarn --mode=skip-build && yarn patch-package

      - name: Run postinstall only (cache hit)
        if: steps.yarn-cache.outputs.cache-hit == 'true'
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_OPTIONS: '--max_old_space_size=4096'
        run: |
          yarn postinstall

      - name: Run Tests
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_OPTIONS: '--max_old_space_size=8192'
        run: |
          yarn test

      - name: Upload Test Report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-report
          path: test-report.html


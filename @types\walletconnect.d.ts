declare module '@walletconnect/modal-react-native/lib/module/controllers/AccountCtrl' {
  export * from '@walletconnect/modal-react-native/lib/typescript/controllers/AccountCtrl';
}

declare module '@walletconnect/modal-react-native/lib/module/controllers/ClientCtrl' {
  export * from '@walletconnect/modal-react-native/lib/typescript/controllers/ClientCtrl';
}

declare module '@walletconnect/modal-react-native/lib/module/controllers/WcConnectionCtrl' {
  export * from '@walletconnect/modal-react-native/lib/typescript/controllers/WcConnectionCtrl';
}

declare module '@walletconnect/modal-react-native/lib/module/hooks/useWalletConnectModal' {
  export * from '@walletconnect/modal-react-native/lib/typescript/hooks/useWalletConnectModal';
}

declare module '@walletconnect/modal-react-native/lib/module/modal/wcm-modal' {
  export * from '@walletconnect/modal-react-native/lib/typescript/modal/wcm-modal';
}

declare module '@walletconnect/modal-react-native/lib/module/utils/StorageUtil' {
  export * from '@walletconnect/modal-react-native/lib/typescript/utils/StorageUtil';
}

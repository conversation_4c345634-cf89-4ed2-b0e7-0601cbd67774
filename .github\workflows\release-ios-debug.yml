name: release-ios-debug

on:
  workflow_dispatch:
    inputs:
      is-split-bundle:
        description: "Should it run as split-bundle? (keep it null if you don't need it)"
        required: false
        default: ''

jobs:
  release-ios-debug:
    runs-on: macos-14
    strategy:
      matrix:
        node-version: [20.x]
        ruby-version: [2.7.x]
        xcode-version: [15.3]

    steps:
      - name: Show executed time
        run: |
          echo "Executed at: $(date '+%Y-%m-%d %H:%M:%S')"

      - name: Checkout Source Code
        uses: actions/checkout@v3
        with:
          lfs: true

      - name: Run Shared Env Setup
        uses: ./.github/actions/shared-env
        with:
          env_file_name: '.env.expo'
          sentry_project: ''
          covalent_key: ${{ secrets.COVALENT_KEY }}
          sentry_token: ${{ secrets.SENTRY_TOKEN }}
          sentry_dsn_react_native: ${{ secrets.SENTRY_DSN_REACT_NATIVE }}
          sentry_dsn_web: ${{ secrets.SENTRY_DSN_WEB }}
          sentry_dsn_desktop: ${{ secrets.SENTRY_DSN_DESKTOP }}
          sentry_dsn_mas: ${{ secrets.SENTRY_DSN_MAS }}
          sentry_dsn_snap: ${{ secrets.SENTRY_DSN_SNAP }}
          sentry_dsn_winms: ${{ secrets.SENTRY_DSN_WINMS }}
          sentry_dsn_ext: ${{ secrets.SENTRY_DSN_EXT }}

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: 'https://npm.pkg.github.com'
          always-auth: true
          scope: '@onekeyhq'

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn config get cacheFolder)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v3
        id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Show SDKs
        run: |
          xcodebuild -showsdks

      - name: Install Dep
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_OPTIONS: '--max_old_space_size=8192'
        run: |
          yarn

      - name: Install Pods
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_OPTIONS: '--max_old_space_size=8192'
        run: |
          cd apps/mobile/ios
          gem install cocoapods -v 1.15.2
          pod install

      - name: Build App File
        env:
          NODE_OPTIONS: '--max_old_space_size=8192'
          SENTRY_DISABLE_AUTO_UPLOAD: 'true'
        run: |
          yarn app:web-embed:build
          cd apps/mobile/ios
          xcodebuild -workspace OneKeyWallet.xcworkspace -configuration Debug -scheme OneKeyWallet -sdk iphonesimulator -derivedDataPath ./outputs

      - name: Zip artifact
        run: |
          cd apps/mobile/ios/outputs/Build/Products/Debug-iphonesimulator
          zip OneKeyWallet-Debug.zip OneKeyWallet.app/* -r

      - name: Upload App File
        uses: actions/upload-artifact@v4
        with:
          name: release-App-debug
          path: |
            ./apps/mobile/ios/outputs/Build/Products/Debug-iphonesimulator/OneKeyWallet-Debug.zip
          if-no-files-found: error

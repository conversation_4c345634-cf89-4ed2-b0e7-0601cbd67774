{"name": "@onekeyhq/ext", "version": "1.0.0", "main": "index.js", "scripts": {"start": "WEB_PORT=3180 webpack serve", "start:proxy": "ONEKEY_PROXY=true yarn start", "clean": "yarn clean:build", "clean:build": "rimraf ./build && rimraf .tamagui && rimraf ./node_modules/.cache", "build": "cross-env NODE_ENV=production webpack build", "build:all": "yarn clean && yarn build && node ../../development/webpack/ext/zip.js", "start:v3": "EXT_MANIFEST_V3=1 WEB_PORT=3180 NODE_OPTIONS=\"--max-old-space-size=8192\" webpack serve", "start:v3:proxy": "ONEKEY_PROXY=true yarn start:v3", "build:v3": "cross-env NODE_ENV=production EXT_MANIFEST_V3=1 NODE_OPTIONS=\"--max-old-space-size=8192\"  webpack build", "build:all:v3": "yarn clean && yarn build:v3 && node ../../development/webpack/ext/zip.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "_folderslint": "yarn folderslint"}, "private": true, "dependencies": {"@onekeyhq/components": "*", "@onekeyhq/kit": "*", "@onekeyhq/shared": "*"}, "devDependencies": {"folderslint": "^1.2.0", "rimraf": "^3"}}
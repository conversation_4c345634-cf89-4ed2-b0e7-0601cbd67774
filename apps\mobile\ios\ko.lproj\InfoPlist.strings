/* 
  InfoPlist.strings
  OneKeyWallet

  Created by <PERSON> on 2024/3/8.
  
*/

"NFCReaderUsageDescription" = "NFC를 사용하여 NDEF 메시지를 애플리케이션으로 읽어옵니다.";
"NSBluetoothAlwaysUsageDescription" = "OneKey 하드웨어 장치를 연결하기 위해 블루투스를 사용합니다.";
"NSBluetoothPeripheralUsageDescription" = "OneKey 하드웨어 장치를 연결하기 위해 블루투스 주변 장치를 사용합니다.";
"NSCameraUsageDescription" = "카메라를 사용하여 QR 코드를 스캔합니다.";
"NSFaceIDUsageDescription" = "Face ID를 사용하여 지갑을 인증하고 잠금을 해제합니다.";
"NSMicrophoneUsageDescription" = "비디오 녹화를 위해 마이크를 사용합니다.";
"NSPhotoLibraryAddUsageDescription" = "사진 보관함을 사용하여 QR 이미지를 저장합니다.";
"NSPhotoLibraryUsageDescription" = "사진 보관함을 사용하여 QR 이미지를 읽습니다.";
"NSLocalNetworkUsageDescription" = "이 앱은 사용자가 사용하는 네트워크에서 장치를 발견하고 연결할 수 있습니다.";

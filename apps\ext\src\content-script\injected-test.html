<!DOCTYPE html>
<html lang="en">
  <head>
    <script>
      for (let i = 0; i < 20; i++) {
        fetch('https://fiat.onekeycn.com/swap/config/?i=' + i);
      }

      if (window.$onekey && window.$onekey.ethereum) {
        window.$onekey.ethereum
          .request({ method: 'net_version' })
          .then((res) => {
            console.log('>>>>>>>>>>>>>>>> HEAD onload check:   EVM net_version=', res);
          });
      } else {
        console.log('>>>>>>>>>>>>>>>> HEAD script: window.$onekey.ethereum not found');
      }
    </script>
    <meta charset="UTF-8" />
    <title>Injected test</title>
  </head>
  <body>
    <p>Check console, search</p>
    <pre>onload check</pre>
    <script>
      if (window.$onekey && window.$onekey.ethereum) {
        window.$onekey.ethereum
          .request({ method: 'net_version' })
          .then((res) => {
            console.log('>>>>>>>>>>>>>>>> Dom onload check:   EVM net_version=', res);
          });
      } else {
        console.log('>>>>>>>>>>>>>>>> body script: window.$onekey.ethereum not found');
      }
    </script>
  </body>
</html>

name: daily-build-dev

on:
  workflow_dispatch:

jobs:
  daily-build:
    runs-on: ubuntu-latest
    # needs: get-commit-id
    strategy:
      matrix:
        node-version: [20.x]

    steps:
      - name: 'Setup ENV'
        run: |
          echo "Executed at: $(date '+%Y-%m-%d %H:%M:%S')"
          DATE=`date "+%m%d"`
          run_number=$(($GITHUB_RUN_NUMBER % 100))
          run_number=$(printf "%02d" $run_number)
          build_number="1000${DATE}${run_number}"
          echo '$build_number='$build_number
          
          echo "BUILD_NUMBER=$build_number" >> $GITHUB_ENV

      - name: Save workflow parameters
        run: |
          mkdir -p ./workflow-data
          echo "WORKFLOW_GITHUB_SHA=${{ github.sha }}" >> ./workflow-data/params.env
          echo "WORKFLOW_GITHUB_REF_NAME=${{ github.ref_name }}" >> ./workflow-data/params.env
          echo "WORKFLOW_BUILD_NUMBER=${{ env.BUILD_NUMBER }}" >> ./workflow-data/params.env
      
      - name: Upload workflow parameters
        uses: actions/upload-artifact@v4
        with:
          name: workflow-params
          path: workflow-data/params.env
          retention-days: 1
   


   
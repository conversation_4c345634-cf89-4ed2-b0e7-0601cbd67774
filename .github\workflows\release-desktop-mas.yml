name: release-desktop-mas

on:
  workflow_run:
    workflows:
      - daily-build
    types:
      - completed
  workflow_dispatch:

jobs:
  release-desktop-mas:
    runs-on: macos-latest
    strategy:
      matrix:
        node-version: [20.x]

    if: ${{ !github.event.workflow_run || (github.event.workflow_run && github.event.workflow_run.conclusion == 'success') }}
    steps:
      - name: Show executed time
        run: |
          echo "Executed at: $(date '+%Y-%m-%d %H:%M:%S')"

      - name: Checkout Source Code
        uses: actions/checkout@v3
        with:
          lfs: true

      - name: Run Shared Env Setup
        uses: ./.github/actions/shared-env
        with:
          env_file_name: '.env'
          sentry_project: 'desktop-mas'
          covalent_key: ${{ secrets.COVALENT_KEY }}
          sentry_token: ${{ secrets.SENTRY_TOKEN }}
          sentry_dsn_react_native: ${{ secrets.SENTRY_DSN_REACT_NATIVE }}
          sentry_dsn_web: ${{ secrets.SENTRY_DSN_WEB }}
          sentry_dsn_desktop: ${{ secrets.SENTRY_DSN_DESKTOP }}
          sentry_dsn_mas: ${{ secrets.SENTRY_DSN_MAS }}
          sentry_dsn_snap: ${{ secrets.SENTRY_DSN_SNAP }}
          sentry_dsn_winms: ${{ secrets.SENTRY_DSN_WINMS }}
          sentry_dsn_ext: ${{ secrets.SENTRY_DSN_EXT }}

      - name: 'Setup ENV'
        run: |
          eval "$(node -e 'const v=require("./apps/desktop/package.json").version; console.log("pkg_version="+v)')"
          echo '$pkg_version='$pkg_version
          echo "PKG_VERSION=$pkg_version" >> $GITHUB_ENV
          artifacts_url="$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID"
          echo "ARTIFACTS_URL=$artifacts_url" >> $GITHUB_ENV


      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: 'https://npm.pkg.github.com'
          always-auth: true
          scope: '@onekeyhq'

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn config get cacheFolder)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v3
        id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Install Dep
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_OPTIONS: '--max_old_space_size=8192'
        run: |
          yarn

      # - name: Setup Code Signing file
      #   run: |
      #     echo ${{ secrets.DESKTOP_KEYS_SECRET }} | base64 -d > apps/desktop/sign.p12
      - name: Install the Apple certificate and provisioning profile for build mas
        env:
          MAC_INSTALL_P12_BASE64: ${{secrets.MAC_INSTALL_P12_BASE64}}
          MAC_INSTALL_P12_PASSWORD: ${{secrets.MAC_INSTALL_P12_PASSWORD}}
          APPLE_DISTRIBUTION_P12_BASE64: ${{secrets.APPLE_DISTRIBUTION_P12_BASE64}}
          APPLE_DISTRIBUTION_P12_PASSWORD: ${{secrets.APPLE_DISTRIBUTION_P12_PASSWORD}}
          PROVISION_PROFILE_BASE64: ${{secrets.PROVISION_PROFILE_BASE64}}
          KEYCHAIN_PASSWORD: ${{ secrets.KEYCHAIN_PASSWORD }}
        run: |
          # create variables
          MAC_INSTALL_P12_PATH=$RUNNER_TEMP/mac_install_certificate.p12
          APPLE_DISTRIBUTION_P12_PATH=$RUNNER_TEMP/apple_distribution_certificate.p12
          # APPLE_WWDRCA_PATH=$RUNNER_TEMP/apple_WWDRCAG3.cer
          PP_PATH=./apps/desktop/OneKey_Mac_App.provisionprofile
          KEYCHAIN_PATH=$RUNNER_TEMP/app-signing.keychain-db

          # import certificate from secrets
          echo -n "$MAC_INSTALL_P12_BASE64" | base64 --decode > $MAC_INSTALL_P12_PATH
          echo -n "$APPLE_DISTRIBUTION_P12_BASE64" | base64 --decode > $APPLE_DISTRIBUTION_P12_PATH
          echo -n "$PROVISION_PROFILE_BASE64" | base64 --decode > $PP_PATH
          # curl https://www.apple.com/certificateauthority/AppleWWDRCAG3.cer > $APPLE_WWDRCA_PATH

          # create temporary keychain
          security create-keychain -p "$KEYCHAIN_PASSWORD" $KEYCHAIN_PATH
          security set-keychain-settings -lut 21600 $KEYCHAIN_PATH
          security unlock-keychain -p "$KEYCHAIN_PASSWORD" $KEYCHAIN_PATH

          # import certificate to keychain
          security import $MAC_INSTALL_P12_PATH -P "$MAC_INSTALL_P12_PASSWORD" -A -t cert -f pkcs12 -k $KEYCHAIN_PATH
          security import $APPLE_DISTRIBUTION_P12_PATH -P "$APPLE_DISTRIBUTION_P12_PASSWORD" -A -t cert -f pkcs12 -k $KEYCHAIN_PATH
          # security import $APPLE_WWDRCA_PATH -A -t cert -k $KEYCHAIN_PATH
          security list-keychain -d user -s $KEYCHAIN_PATH

          # apply provisioning profile
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          cp $PP_PATH ~/Library/MobileDevice/Provisioning\ Profiles

      - name: Build and Sign Static MAS
        env:
          NODE_OPTIONS: '--max_old_space_size=8192'
          APPLEID: ${{ secrets.APPLEID }}
          APPLEIDPASS: ${{ secrets.APPLEIDPASS }}
          ASC_PROVIDER: ${{ secrets.ASC_PROVIDER }}
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: 'cd apps/desktop && yarn build:mas'

      - name: Clean up keychain and provisioning profile
        if: ${{ always() }}
        run: |
          security delete-keychain $RUNNER_TEMP/app-signing.keychain-db
          rm ~/Library/MobileDevice/Provisioning\ Profiles/OneKey_Mac_App.provisionprofile

      - name: Upload Artifacts mas
        uses: actions/upload-artifact@v4
        with:
          name: onekey-desktop-mas
          path: |
            ./apps/desktop/build-electron/mas-universal/*.pkg

      - name: validate mas for Testflight
        env:
          APPLEID: ${{ secrets.APPLEID }}
          APPLEIDPASS: ${{ secrets.APPLEIDPASS }}
        run: |
          xcrun altool --validate-app --f ./apps/desktop/build-electron/mas-universal/*.pkg -t macOS -u $APPLEID -p $APPLEIDPASS --show-progress

      - name: upload mas for Testflight
        env:
          APPLEID: ${{ secrets.APPLEID }}
          APPLEIDPASS: ${{ secrets.APPLEIDPASS }}
        run: |
          xcrun altool --upload-app --f ./apps/desktop/build-electron/mas-universal/*.pkg -t macOS -u $APPLEID -p $APPLEIDPASS --show-progress

      - name: 'Notify to Slack'
        if: ${{ github.event.workflow_run }}
        uses: onekeyhq/actions/slack-notify-webhook@main
        with:
          web-hook-url: ${{ secrets.SLACK_NOTIFICATION_WEBHOOK }}
          secret-key: ${{ secrets.ACTION_SIGN_SECERT_KEY }}
          artifact-type: Desktop
          artifact-name: OneKey-Desktop-MAS
          artifact-bundle-id: 'so.onekey.wallet'
          artifact-version-name: '${{ env.BUILD_APP_VERSION }}@${{ env.BUILD_NUMBER }}'
          artifact-version-code: '${{ env.BUILD_NUMBER }}'
          artifact-download-url: '${{ env.ARTIFACTS_URL }}'

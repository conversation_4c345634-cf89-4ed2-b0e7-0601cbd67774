<!DOCTYPE html>
<html lang="" style="width: 375px; height: 600px; margin: 0; ">
  <head>
    <meta charset="utf-8" />
    <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
    <!--
        This viewport works for phones with notches.
        It's optimized for gestures by disabling global zoom.
    -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1.00001, viewport-fit=cover"
    />
    <title></title>
    <style>
      /* add global styles to packages/shared/src/web/index.css */
    </style>
<!--    <script>-->
<!--      // ** inline script not allowed in Ext, use themePreload.js instead-->
<!--      // document.documentElement.style.backgroundColor = 'black';-->
<!--    </script>-->
    <script src="/preload-html-head.js"></script>
  </head>

  <!-- html template copy from node_modules/@expo/webpack-config/web-default/index.html -->
  <body class="onekey-body"
        style="width: 375px; height: 600px; margin: 0; "
        data-platform="ext"
        data-browser="chrome"
        data-filename="ui-popup.html"


  >



    <!--
      A generic no script element with a reload button and a message.
      Feel free to customize this however you'd like.
    -->
    <noscript>
      <form
        action=""
        style="
          background-color: #fff;
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 9999;
        "
      >
        <div
          style="
            font-size: 18px;
            font-family: Helvetica, sans-serif;
            line-height: 24px;
            margin: 10%;
            width: 80%;
          "
        >
          <p>Oh no! It looks like JavaScript is not enabled in your browser.</p>
          <p style="margin: 20px 0">
            <button
              type="submit"
              style="
                background-color: #4630eb;
                border-radius: 100px;
                border: none;
                box-shadow: none;
                color: #fff;
                cursor: pointer;
                font-weight: bold;
                line-height: 20px;
                padding: 6px 16px;
              "
            >
              Reload
            </button>
          </p>
        </div>
      </form>
    </noscript>

    <!-- The root element for your Expo app.  -->
    <div id="root"></div>
  <script src="/ui-popup-boot.js"></script>

  </body>
</html>

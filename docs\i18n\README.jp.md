
## 🌍 環境設定

1. [node.js LTS バージョン (>= 16)] をインストールする(https://nodejs.org/en/)
2. [yarnパッケージ管理ツール]（https://yarnpkg.com/）バージョン1.18.0をインストールします。 （最新バージョンのyarnをインストールした後、ルートディレクトリで `yarn policy set-version 1.18.0`を実行します）
3. インストール [git lfs](https://git-lfs.github.com/) (いくつかのバイナリのプルおよびアップデートに必要)
4. iOSプロジェクトを開始するには、ローカルXCodeバージョンが13.3以上であることを確認してください
5. Androidプロジェクトを開始するには、ローカルJDKバージョンが11以上であることを確認してください

git コマンドラインツールで最新のコードを取得した後、プロジェクトの依存関係を ``yarn`` コマンドでルートディレクトリにインストールします。

すべてのJS依存とサブモジュールの依存をインストールします。

```
yarn
```

## 😎 開発

ルートディレクトリで以下のコマンドを実行し、異なるビジネスコードを開発します。

- `yarn app:web`: ウェブモードを開発し、ポート 3000 の静的サーバをローカルに起動します。
- `yarn app:ios`: USB接続でiphoneデバイスの開発をデバッグする。
- `yarn app:android`: Androidのデバッグ
- `yarn app:desktop`: デスクトップモードでの開発
- `yarn app:ext`: ブラウザプラグインを開発します

### Androidプロジェクトの構成

#### 第一の方法：コミュニティ開発者向け

パッケージ/アプリ/アンドロイド/lib-keys-secret/src/main/cpp/keys.c`で関連するキーを設定するか、デフォルトのオプションを使用します。 一部のAPIには制限がある場合があります。

#### 第二の方法：公式開発者向け

1.暗号化リポジトリから `debug.keystore` ファイルを取得し、 `apps/android/keystores` ディレクトリに配置します（ない場合は自分で作成してください）。
2.暗号化リポジトリで `keys.secret` ファイルを取得し、`apps/android` ディレクトリに配置します。

## 🗂 複数のリポジトリディレクトリ構造

リポジトリはmonorepoモデルで構成され、異なるエンドのコードを一元化して影響を受けないようにするとともに、パッケージングとコンパイルの過程で可能な限りコードの再利用を可能にします。

- `packages/components` はUIコンポーネントを保持します
- `packages/kit`再利用可能なページレベルのUIコンテンツ用の
- `packages /app`APPコード
- `apps/desktop`デスクトップの電子コード
- `packages /web`Webコード
- `apps/ext`プラグインサイドコード

## 🧲 依存関係をインストールする

packages/` ディレクトリの下のサブディレクトリはそれぞれ別のプロジェクトで、その名前は monorepo の **package.json** ディレクトリにある `name` フィールドの値になります。

サブディレクトリの依存関係をインストールする必要があるときは、`yarn workspace @onekeyhq/web add axios`とすればよいでしょう。 yarn workspace @onekeyhq/web` のような接頭辞をつけると、最終的に axios モジュールは web サブプロジェクトのルートディレクトリにインストールされるようになります。

依存関係の中にはネイティブな部分もあるので、JSの依存関係をインストールした後に、`apps/ios`ディレクトリに移動して、`pod install`を実行する必要があります。

## 😷 よくある質問

1.アプリを起動できず、さまざまな環境起動の問題

起動フェーズでの環境、モジュール、および依存関係の問題については、最初にルートディレクトリでコマンド`yarnclean`を使用することをお勧めします。このコマンドは、すべてのサブ依存関係、yarnのモジュールキャッシュ、metro / babelなどのツールのキャッシュをクリアしてから、プロジェクトを再起動して試行します。

2.依存関係のインストール中、または新しい依存関係を追加するときに、yarnは**エラーをプロンプトします予期しないエラーが発生しました："予期されたワークスペースパッケージが存在します**

https://github.com/yarnpkg/yarn/issues/7807、コマンド「yarnpolicyset-version1.18.0」を使用して現在の環境のyarnバージョンを1.18.0に設定します。

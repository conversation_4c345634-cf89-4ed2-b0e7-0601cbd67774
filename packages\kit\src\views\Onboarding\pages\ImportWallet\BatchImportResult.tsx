import { useCallback, useEffect, useState } from 'react';
import { useIntl } from 'react-intl';

import {
  Alert,
  Badge,
  Button,
  Icon,
  ListView,
  Page,
  Progress,
  SizableText,
  Stack,
  XStack,
} from '@onekeyhq/components';
import backgroundApiProxy from '@onekeyhq/kit/src/background/instance/backgroundApiProxy';
import { ListItem } from '@onekeyhq/kit/src/components/ListItem';
import useAppNavigation from '@onekeyhq/kit/src/hooks/useAppNavigation';
import { ETranslations } from '@onekeyhq/shared/src/locale';
import { EOnboardingPages } from '@onekeyhq/shared/src/routes';
import type { IOnboardingParamList } from '@onekeyhq/shared/src/routes';
import timerUtils from '@onekeyhq/shared/src/utils/timerUtils';

type IRouteParams = IOnboardingParamList[EOnboardingPages.BatchImportResult];

type IImportResult = {
  index: number;
  input: string;
  success: boolean;
  error?: string;
  walletId?: string;
  accountId?: string;
};

export function BatchImportResult() {
  const intl = useIntl();
  const navigation = useAppNavigation();

  // 获取路由参数
  const routeParams = navigation.getParams<IRouteParams>();
  const { importType, totalCount, results: initialResults } = routeParams;

  // 状态管理
  const [isImporting, setIsImporting] = useState(true);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [results, setResults] = useState<IImportResult[]>(initialResults);
  const [successCount, setSuccessCount] = useState(0);
  const [failedCount, setFailedCount] = useState(0);

  // 计算进度
  const progress = Math.round((currentIndex / totalCount) * 100);
  const isCompleted = currentIndex >= totalCount;

  // 执行批量导入
  const performBatchImport = useCallback(async () => {
    setIsImporting(true);
    setCurrentIndex(0);
    setSuccessCount(0);
    setFailedCount(0);

    try {
      const walletData = results.map((item, index) => ({
        input: item.input,
        name: `Wallet ${index + 1}`,
      }));

      const batchResult = await backgroundApiProxy.serviceAccount.batchImportWallets({
        importType,
        walletData,
        networkId: 'evm--1',
        onProgress: (current, total) => {
          setCurrentIndex(current);
        },
      });

      // 更新结果
      setResults(batchResult.results);
      setSuccessCount(batchResult.successCount);
      setFailedCount(batchResult.failedCount);

    } catch (error) {
      // 处理整体错误
      setResults(prev => prev.map(result => ({
        ...result,
        success: false,
        error: (error as Error).message,
      })));
      setFailedCount(results.length);
      setSuccessCount(0);
    }

    setIsImporting(false);
  }, [results, importType]);

  // 组件挂载时开始导入
  useEffect(() => {
    void performBatchImport();
  }, []);

  // 重试失败的导入
  const handleRetryFailed = useCallback(async () => {
    const failedItems = results.filter(item => !item.success);
    if (failedItems.length === 0) return;

    setIsImporting(true);
    setCurrentIndex(0);

    try {
      const walletData = failedItems.map((item, index) => ({
        input: item.input,
        name: `Wallet ${item.index + 1}`,
      }));

      const batchResult = await backgroundApiProxy.serviceAccount.batchImportWallets({
        importType,
        walletData,
        networkId: 'evm--1',
        onProgress: (current, total) => {
          setCurrentIndex(current);
        },
      });

      // 更新失败项的结果
      let retrySuccessCount = 0;
      let retryFailedCount = 0;

      setResults(prev => prev.map(result => {
        if (!result.success) {
          const retryResult = batchResult.results.find(r =>
            failedItems[r.index]?.input === result.input
          );
          if (retryResult) {
            if (retryResult.success) {
              retrySuccessCount++;
            } else {
              retryFailedCount++;
            }
            return {
              ...result,
              success: retryResult.success,
              error: retryResult.error,
              walletId: retryResult.walletId,
              accountId: retryResult.accountId,
            };
          }
        }
        return result;
      }));

      setSuccessCount(prev => prev + retrySuccessCount);
      setFailedCount(prev => prev - retrySuccessCount);

    } catch (error) {
      // 重试失败，保持原有错误状态
      console.error('Retry failed:', error);
    }

    setIsImporting(false);
  }, [results, importType]);

  // 完成导入
  const handleFinish = useCallback(() => {
    navigation.popStack();
  }, [navigation]);

  // 渲染结果列表项
  const renderResultItem = useCallback(({ item, index }: { item: IImportResult; index: number }) => {
    const displayInput = item.input.length > 20 
      ? `${item.input.slice(0, 20)}...` 
      : item.input;

    return (
      <ListItem
        key={`result-${index}`}
        title={`Wallet ${index + 1}`}
        subtitle={item.success ? displayInput : item.error}
        subtitleProps={{
          color: item.success ? '$textSubdued' : '$textCritical',
          fontFamily: item.success ? '$mono' : '$body',
        }}
      >
        <ListItem.Avatar>
          <Badge
            size="sm"
            variant={item.success ? 'success' : 'destructive'}
          >
            {item.success ? '✓' : '✗'}
          </Badge>
        </ListItem.Avatar>
        <ListItem.Text
          primary={`Wallet ${index + 1}`}
          secondary={item.success ? displayInput : item.error}
          secondaryTextProps={{
            color: item.success ? '$textSubdued' : '$textCritical',
            fontFamily: item.success ? '$mono' : '$body',
            numberOfLines: 2,
          }}
        />
      </ListItem>
    );
  }, []);

  return (
    <Page scrollEnabled>
      <Page.Header
        title={intl.formatMessage({
          id: ETranslations.global_batch_import_result,
        })}
      />
      <Page.Body px="$5">
        <Stack gap="$4">
          {/* 进度显示 */}
          <Stack alignItems="center" gap="$4">
            {isCompleted && !isImporting ? (
              <Icon 
                name={successCount === totalCount ? "CheckRadioSolid" : "XCircleSolid"} 
                size="$12" 
                color={successCount === totalCount ? "$iconSuccess" : "$iconCritical"} 
              />
            ) : (
              <Progress
                w="100%"
                size="medium"
                value={progress}
              />
            )}

            <XStack alignItems="center" gap="$2">
              <SizableText size="$bodyLg" textAlign="center">
                {isImporting 
                  ? intl.formatMessage(
                      { id: ETranslations.global_importing_progress },
                      { current: currentIndex, total: totalCount }
                    )
                  : intl.formatMessage({ id: ETranslations.global_import_completed })
                }
              </SizableText>
            </XStack>
          </Stack>

          {/* 统计信息 */}
          <Stack
            direction="row"
            gap="$2"
            alignItems="center"
            justifyContent="space-between"
            p="$3"
            bg="$bgSubdued"
            borderRadius="$3"
          >
            <SizableText size="$bodyMd" color="$textSubdued">
              {intl.formatMessage({ id: ETranslations.global_import_summary })}
            </SizableText>
            <Stack direction="row" gap="$2" alignItems="center">
              <Badge size="sm" variant="success">
                {intl.formatMessage(
                  { id: ETranslations.global_success_count },
                  { count: successCount }
                )}
              </Badge>
              {failedCount > 0 && (
                <Badge size="sm" variant="destructive">
                  {intl.formatMessage(
                    { id: ETranslations.global_failed_count },
                    { count: failedCount }
                  )}
                </Badge>
              )}
              <Badge size="sm" variant="neutral">
                {intl.formatMessage(
                  { id: ETranslations.global_total_count },
                  { count: totalCount }
                )}
              </Badge>
            </Stack>
          </Stack>

          {/* 错误提示 */}
          {failedCount > 0 && isCompleted && (
            <Alert
              type="warning"
              title={intl.formatMessage({
                id: ETranslations.global_batch_import_partial_failure,
              })}
              description={intl.formatMessage({
                id: ETranslations.global_batch_import_partial_failure_description,
              })}
            />
          )}

          {/* 结果列表 */}
          {isCompleted && (
            <Stack flex={1}>
              <SizableText size="$headingSm" mb="$3">
                {intl.formatMessage({ id: ETranslations.global_import_results })}
              </SizableText>
              <ListView
                data={results}
                renderItem={renderResultItem}
                keyExtractor={(item, index) => `result-${index}`}
                estimatedItemSize={80}
                showsVerticalScrollIndicator={false}
              />
            </Stack>
          )}
        </Stack>
      </Page.Body>
      <Page.Footer>
        {isCompleted && (
          <Stack direction="row" gap="$3" w="100%">
            {failedCount > 0 && (
              <Button
                flex={1}
                variant="secondary"
                onPress={handleRetryFailed}
                disabled={isImporting}
              >
                {intl.formatMessage({ id: ETranslations.global_retry_failed })}
              </Button>
            )}
            <Button
              flex={1}
              onPress={handleFinish}
              disabled={isImporting}
            >
              {intl.formatMessage({ id: ETranslations.global_done })}
            </Button>
          </Stack>
        )}
      </Page.Footer>
    </Page>
  );
}

export default BatchImportResult;

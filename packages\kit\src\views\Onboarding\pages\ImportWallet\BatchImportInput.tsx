import { useCallback, useMemo, useState } from 'react';
import { useIntl } from 'react-intl';

import {
  <PERSON><PERSON>,
  Badge,
  Button,
  Form,
  Page,
  SizableText,
  Stack,
  TextAreaInput,
  useForm,
} from '@onekeyhq/components';
import useAppNavigation from '@onekeyhq/kit/src/hooks/useAppNavigation';
import { ETranslations } from '@onekeyhq/shared/src/locale';
import { EOnboardingPages } from '@onekeyhq/shared/src/routes';
import type { IOnboardingParamList } from '@onekeyhq/shared/src/routes';

type IFormValues = {
  inputData: string;
};

type IRouteParams = IOnboardingParamList[EOnboardingPages.BatchImportInput];

export function BatchImportInput() {
  const intl = useIntl();
  const navigation = useAppNavigation();
  const [isValidating, setIsValidating] = useState(false);

  // 获取路由参数
  const routeParams = navigation.getParams<IRouteParams>();
  const { importType } = routeParams;

  const form = useForm<IFormValues>({
    defaultValues: {
      inputData: '',
    },
  });

  const inputData = form.watch('inputData');

  // 解析输入数据
  const parsedData = useMemo(() => {
    if (!inputData.trim()) {
      return {
        lines: [],
        validCount: 0,
        invalidCount: 0,
        totalCount: 0,
      };
    }

    const lines = inputData
      .split('\n')
      .map((line, index) => line.trim())
      .filter((line) => line.length > 0)
      .map((line, index) => ({
        index,
        input: line,
        isValid: validateInput(line, importType),
        error: validateInput(line, importType) ? undefined : getValidationError(line, importType),
      }));

    const validCount = lines.filter((line) => line.isValid).length;
    const invalidCount = lines.filter((line) => !line.isValid).length;

    return {
      lines,
      validCount,
      invalidCount,
      totalCount: lines.length,
    };
  }, [inputData, importType]);

  // 验证单个输入
  const validateInput = useCallback((input: string, type: 'privateKey' | 'mnemonic'): boolean => {
    if (!input.trim()) return false;

    if (type === 'privateKey') {
      // 简单的私钥格式验证（十六进制，64字符）
      const privateKeyRegex = /^(0x)?[a-fA-F0-9]{64}$/;
      return privateKeyRegex.test(input.trim());
    } else if (type === 'mnemonic') {
      // 简单的助记词验证（12或24个单词）
      const words = input.trim().split(/\s+/);
      return words.length === 12 || words.length === 24;
    }

    return false;
  }, []);

  // 获取验证错误信息
  const getValidationError = useCallback((input: string, type: 'privateKey' | 'mnemonic'): string => {
    if (!input.trim()) {
      return intl.formatMessage({ id: ETranslations.form_required });
    }

    if (type === 'privateKey') {
      return intl.formatMessage({ id: ETranslations.form_private_key_error_invalid });
    } else if (type === 'mnemonic') {
      return intl.formatMessage({ id: ETranslations.form_mnemonic_error_invalid });
    }

    return intl.formatMessage({ id: ETranslations.form_invalid_input });
  }, [intl]);

  // 处理继续按钮点击
  const handleContinue = useCallback(async () => {
    if (parsedData.validCount === 0) {
      return;
    }

    setIsValidating(true);
    try {
      // 导航到预览页面
      navigation.push(EOnboardingPages.BatchImportPreview, {
        importType,
        inputData: parsedData.lines.map(line => line.input),
        parsedData: parsedData.lines,
      });
    } finally {
      setIsValidating(false);
    }
  }, [navigation, importType, parsedData]);

  const getPlaceholderText = useCallback(() => {
    if (importType === 'privateKey') {
      return intl.formatMessage({
        id: ETranslations.global_batch_import_private_key_placeholder,
      });
    } else {
      return intl.formatMessage({
        id: ETranslations.global_batch_import_mnemonic_placeholder,
      });
    }
  }, [importType, intl]);

  const getInstructionText = useCallback(() => {
    if (importType === 'privateKey') {
      return intl.formatMessage({
        id: ETranslations.global_batch_import_private_key_instruction,
      });
    } else {
      return intl.formatMessage({
        id: ETranslations.global_batch_import_mnemonic_instruction,
      });
    }
  }, [importType, intl]);

  return (
    <Page scrollEnabled>
      <Page.Header
        title={intl.formatMessage({
          id: importType === 'privateKey' 
            ? ETranslations.global_batch_import_private_key
            : ETranslations.global_batch_import_mnemonic,
        })}
      />
      <Page.Body px="$5">
        <Stack gap="$4">
          {/* 说明文本 */}
          <Alert
            type="info"
            title={intl.formatMessage({
              id: ETranslations.global_batch_import_instruction_title,
            })}
            description={getInstructionText()}
          />

          {/* 输入表单 */}
          <Form form={form}>
            <Form.Field
              name="inputData"
              label={intl.formatMessage({
                id: importType === 'privateKey' 
                  ? ETranslations.global_private_key
                  : ETranslations.global_recovery_phrase,
              })}
              rules={{
                required: intl.formatMessage({ id: ETranslations.form_required }),
                validate: (value: string) => {
                  if (!value.trim()) {
                    return intl.formatMessage({ id: ETranslations.form_required });
                  }
                  const lines = value.split('\n').filter(line => line.trim());
                  if (lines.length === 0) {
                    return intl.formatMessage({ id: ETranslations.form_required });
                  }
                  return undefined;
                },
              }}
            >
              <TextAreaInput
                placeholder={getPlaceholderText()}
                numberOfLines={10}
                size="large"
                multiline
              />
            </Form.Field>
          </Form>

          {/* 统计信息 */}
          {parsedData.totalCount > 0 && (
            <Stack
              direction="row"
              gap="$2"
              alignItems="center"
              justifyContent="space-between"
              p="$3"
              bg="$bgSubdued"
              borderRadius="$3"
            >
              <SizableText size="$bodyMd" color="$textSubdued">
                {intl.formatMessage({ id: ETranslations.global_total })}
              </SizableText>
              <Stack direction="row" gap="$2" alignItems="center">
                <Badge size="sm" variant="success">
                  {intl.formatMessage(
                    { id: ETranslations.global_valid_count },
                    { count: parsedData.validCount }
                  )}
                </Badge>
                {parsedData.invalidCount > 0 && (
                  <Badge size="sm" variant="destructive">
                    {intl.formatMessage(
                      { id: ETranslations.global_invalid_count },
                      { count: parsedData.invalidCount }
                    )}
                  </Badge>
                )}
                <Badge size="sm" variant="neutral">
                  {intl.formatMessage(
                    { id: ETranslations.global_total_count },
                    { count: parsedData.totalCount }
                  )}
                </Badge>
              </Stack>
            </Stack>
          )}
        </Stack>
      </Page.Body>
      <Page.Footer
        confirmButtonProps={{
          disabled: parsedData.validCount === 0 || isValidating,
          loading: isValidating,
        }}
        onConfirm={handleContinue}
        confirmButtonText={intl.formatMessage({
          id: ETranslations.global_continue,
        })}
      />
    </Page>
  );
}

export default BatchImportInput;

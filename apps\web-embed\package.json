{"name": "@onekeyhq/web-embed", "version": "1.0.0", "main": "index.js", "scripts": {"start": "WEB_PORT=3008 webpack serve", "clean": "yarn clean:build", "clean:build": "rimraf ./web-build && rimraf .tamagui && rimraf ./node_modules/.cache", "build": "cross-env NODE_ENV=production webpack build && sh ./postbuild.sh", "_folderslint": "yarn folderslint"}, "private": true, "dependencies": {"@onekeyhq/components": "*", "@onekeyhq/kit": "*", "@onekeyhq/shared": "*"}, "devDependencies": {"cross-env": "^7.0.3", "folderslint": "^1.2.0", "rimraf": "^3"}}
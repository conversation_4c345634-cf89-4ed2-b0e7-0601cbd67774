<resources xmlns:tools="http://schemas.android.com/tools">
  <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
    <item name="android:navigationBarColor" tools:targetApi="21">@color/navigationBarColor</item>
    <item name="android:textColor">@android:color/black</item>
    <item name="android:editTextStyle">@style/ResetEditText</item>
    <item name="android:editTextBackground" tools:targetApi="11">@drawable/rn_edit_text_material</item>
    <item name="android:fontFamily" tools:targetApi="16">@font/roboto</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
    <item name="android:windowSplashScreenBackground" tools:targetApi="31">@color/splashscreen_background</item>
<!--      <item name="android:windowSplashScreenBrandingImage" tools:targetApi="31">-->
<!--        @drawable/splashscreen-->
<!--      </item>-->
    <item name="android:windowSplashScreenAnimatedIcon" tools:targetApi="31">@drawable/onekey_ic_launcher_foreground</item>
    <item name="android:windowSplashScreenBehavior" tools:targetApi="33">default</item>
  </style>
  <style name="ResetEditText" parent="@android:style/Widget.EditText">
    <item name="android:padding">0dp</item>
    <item name="android:textColorHint">#c8c8c8</item>
    <item name="android:textColor">@android:color/black</item>
  </style>
  <style name="Theme.App.SplashScreen" parent="AppTheme">
    <item name="android:windowBackground">@drawable/splashscreen</item>
  </style>
</resources>

{"name": "@onekeyhq/desktop", "version": "1.0.0", "description": "Multi-chain support for BTC/ETH/BNB/NEAR/Polygon/Solana/Avalanche/Fantom and others", "private": true, "main": "index.js", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "scripts": {"postinstall": "yarn keytar", "keytar": "node development/build_keytar.js", "clean": "rimraf ./build-electron && rimraf .tamagui && rimraf ./app/build && rimraf ./app/dist && rimraf __generated__", "clean:build": "rimraf ./build-electron && rimraf ./app/build && rimraf ./app/dist && rimraf ./node_modules/.cache", "start": "cross-env NODE_OPTIONS=\"--max-old-space-size=8192\" yarn dev", "install-app-deps": "electron-builder install-app-deps", "dev": "yarn keytar && npx concurrently \"yarn build:main:dev\" \"yarn dev:renderer\" \"cross-env LAUNCH_ELECTRON=true node scripts/dev.js\"", "dev:main": "electron --inspect=5858 app/dist/app.js", "dev:renderer": "TRANSFORM_REGENERATOR_DISABLED=true BROWSER=none WEB_PORT=3001 webpack serve", "build:main:dev": "rimraf ./app/dist && cross-env node scripts/build.js", "build:main": "rimraf ./app/dist && cross-env NODE_ENV=production node scripts/build.js", "build:renderer": "rm -rf ./app/build && rm -rf ./web-build && NODE_ENV=production webpack build && mv ./web-build ./app/build && rsync -a public/static/ app/build/static", "build:electron": "electron-builder build -mwl --config electron-builder.config.js", "build:electron:winms": "electron-builder build -w --config electron-builder-ms.config.js", "build:electron:mac": "electron-builder build -m --config electron-builder.config.js", "build:electron:mas": "electron-builder build -m --config electron-builder-mas.config.js", "build:electron:snap": "electron-builder build -l --config electron-builder-snap.config.js", "build": "yarn keytar && NODE_ENV=production sh -c \"yarn clean:build && yarn build:renderer && yarn build:main && yarn install-app-deps && yarn build:electron --publish never\"", "build:snap": "NODE_ENV=production && sh -c \"yarn clean:build && yarn build:renderer && yarn build:main && yarn install-app-deps && yarn build:electron:snap --publish never\"", "build:mac": "NODE_ENV=production sh -c \"yarn clean:build && yarn build:renderer && yarn build:main && yarn install-app-deps && yarn build:electron:mac --publish never\"", "build:mas": "yarn keytar && NODE_ENV=production sh -c \"yarn clean:build && yarn build:renderer && yarn build:main && yarn install-app-deps && yarn build:electron:mas --publish never\"", "build:winms": "NODE_ENV=production && DESK_CHANNEL=ms-store sh -c \"yarn clean:build && yarn build:renderer && yarn build:main && yarn install-app-deps && yarn build:electron:winms --publish never\"", "publish:all": "NODE_ENV=production sh -c \"yarn clean:build && yarn build:renderer && yarn build:main && yarn install-app-deps && yarn build:electron --publish always\"", "publish:winms": "NODE_ENV=production && DESK_CHANNEL=ms-store sh -c \"yarn clean:build && yarn build:renderer && yarn build:main && yarn install-app-deps && yarn build:electron:winms --publish always\"", "_folderslint": "yarn folderslint"}, "dependencies": {"@abandonware/noble": "^1.9.2-26", "@sentry/electron": "5.8.0", "adm-zip": "^0.5.10", "electron-check-biometric-auth-changed": "0.0.6", "electron-context-menu": "^3.5.0", "electron-is-dev": "^2.0.0", "electron-log": "5.2.0", "electron-store": "^8.2.0", "electron-updater": "6.1.8", "electron-windows-security": "0.0.7", "keytar": "^7.9.0", "node-fetch": "^2.6.7"}, "devDependencies": {"@electron/notarize": "2.3.2", "@electron/remote": "^2.0.1", "@types/adm-zip": "^0", "@types/electron-localshortcut": "^3.1.0", "@types/node-fetch": "^2.6.1", "concurrently": "8.2.2", "cross-env": "^7.0.3", "electron": "31.7.7", "electron-builder": "26.0.9", "esbuild": "0.15.18", "folderslint": "^1.2.0", "glob": "^7.2.0", "rimraf": "^3", "webpack": "5.90.3"}}
/* 
  InfoPlist.strings
  OneKeyWallet

  Created by <PERSON> on 2024/3/8.
  
*/

"NFCReaderUsageDescription" = "Use NFC to read NDEF messages into the application.";
"NSBluetoothAlwaysUsageDescription" = "Use bluetooth to connect OneKey hardware devices.";
"NSBluetoothPeripheralUsageDescription" = "Use bluetooth peripheral to connect OneKey hardware devices.";
"NSCameraUsageDescription" = "Use Camera to scan QR Code.";
"NSFaceIDUsageDescription" = "Use Face ID to authenticate and unlock your wallet.";
"NSMicrophoneUsageDescription" = "Use Microphone to record videos.";
"NSPhotoLibraryAddUsageDescription" = "Use photo library to save QR images.";
"NSPhotoLibraryUsageDescription" = "Use photo library to read QR images.";
"NSLocalNetworkUsageDescription" = "This app will be able to discover and connect to devices on the networks you use.";

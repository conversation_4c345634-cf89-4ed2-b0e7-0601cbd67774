{"Limit.expire_day": "Day", "Limit.expire_days": "Days", "Limit.expire_hour": "Hour", "Limit.expire_minutes": "Minutes", "Limit.expire_month": "Month", "Limit.info_order_expires": "Order expires in", "Limit.info_partial_fill": "Partial fill", "Limit.info_partial_fill_disable": "Disable", "Limit.info_partial_fill_enable": "Enable", "Limit.info_provider": "Provider", "Limit.limit_filled": "Filled", "Limit.limit_price": "Limit price", "Limit.market": "Market", "Limit.native_token_no_sell": "Limit order can't sell {token}", "Limit.native_token_no_sell_switch": "Switch to {token}", "Limit.native_token_no_sell_wrap": "Wrap to {token}", "Limit.open_limit_order": "{num} open limit order", "Limit.open_order": "Open orders", "Limit.order_cancel": "Cancel", "Limit.order_expiration": "Expire in", "Limit.order_history": "Order history", "Limit.order_history_created_expiry": "Created | Expiry", "Limit.order_history_empty": "No orders", "Limit.order_history_empty_content": "This account has no orders yet. Place a new one to get started!", "Limit.order_history_filled": "Filled", "Limit.order_history_order_id": "Order ID", "Limit.order_history_status_cancel": "Cancel", "Limit.order_history_status_canceling": "Canceling ...", "Limit.order_history_status_partially_filled": "Partially filled", "Limit.order_history_title": "Order detail", "Limit.order_pair": "Pair", "Limit.order_status": "Status", "Limit.order_status_cancelled": "Cancelled", "Limit.order_status_expired": "Expired", "Limit.order_status_failed": "Failed", "Limit.order_status_filled": "Filled", "Limit.order_status_open": "Open", "Limit.order_status_unfilled": "Unfilled", "account_model.watched": "Watch-Only", "action_save": "Save", "add_hidden_wallet_dialog_add_button_display": "Show \"Add Hidden Wallet\" on wallet list", "add_hidden_wallet_dialog_add_button_display_toast": "You can tap the \"•••\" button next to the wallet name to add a hidden wallet", "add_hidden_wallet_dialog_desc": "A hidden wallet adds a passphrase to your recovery phrase to create a separate, secure wallet.", "add_hidden_wallet_dialog_warning_notice": "Make sure you understand how a passphrase works before continuing. <url>https://help.onekey.so/articles/********-passphrases-and-hidden-wallets<underline>Learn more</underline></url>", "address.verify_address_instruction": "Verify on device to prevent address replacement attacks", "address_book.add_address_add_to_allowlist": "Add to allowlist", "address_book.add_address_name_length_error": "The maximum length is {num} characters.", "address_book.button_close": "Close", "address_book.button_copy": "Copy", "address_book.button_next": "Next", "address_book.button_reset": "Reset", "address_book.confirm_message": "Confirming this operation will reset your address book data. To avoid losing your data entirely, you can copy the data to the clipboard and save it.", "address_book.confirm_title": "Confirm", "address_book.data_anomaly": "Data anomaly", "address_book.data_anomaly_content": "Your address book data may have undergone abnormal changes.", "address_book.data_anomaly_description": "Your contact data may have undergone abnormal changes. To ensure the security of your assets, we strongly recommend resetting your address book.", "address_book.data_anomaly_what_do": "What should I do?", "address_book.data_anomaly_why_reset": "Why do I need to reset my address book?", "address_book.data_anomaly_why_reset_description": "The best way to ensure security is to reset the address book information and recheck the data security. You can click \"Reset\" to start the reset process.", "address_book.data_anomaly_why_risk": "Why is my data at risk?", "address_book.data_anomaly_why_risk_description": "Your data is different from the last confirmation save. One<PERSON>ey doesn't save your data, but ensures that the data will not be tampered with.", "address_book.data_anomaly_will_lost": "Will address book data be lost?", "address_book.data_anomaly_will_lost_description": "After the reset, your address book will be cleared. To avoid data loss, export it by clicking “Copy.” Please handle the exported data with caution.", "address_book.dialog_subtitle": "Discover how the OneKey address book enhances the security of your contact addresses.", "address_book.edit_added_on": "Added on", "address_book.edit_last_edited": "Last edited", "address_book.empty_add_button": "Add", "address_book.empty_description": "You haven't added any address yet.", "address_book.encrypted_storage_description": "OneKey ensures your information security. All your contact data is encrypted and hash-verified with each use to prevent tampering.", "address_book.encrypted_storage_title": "Encrypted storage", "address_book.menu_copy": "Copy", "address_book.menu_edit": "Edit", "address_book.menu_title": "<PERSON><PERSON>", "address_book.no_results_description": "No match found for your search. Please re-enter.", "address_book.no_results_title": "No Results", "address_book.search_placeholder": "Search", "address_book.title": "Address book", "address_book_add_address.add_to_evm_chains": "Also add to additional EVM-compatible Chains", "address_book_add_address.address": "Address", "address_book_add_address.address_exists": "The address already exists.", "address_book_add_address.address_invalid_error": "Invalid address. Please check and re-enter.", "address_book_add_address.address_placeholder": "Address or domain", "address_book_add_address.button_save": "Save", "address_book_add_address.chain": "Network", "address_book_add_address.name": "Name", "address_book_add_address.name_empty_error": "Name cannot be empty.", "address_book_add_address.name_exists": "The name already exists.", "address_book_add_address.name_required": "Required", "address_book_add_address.title": "Add address", "address_book_add_address.toast_add_success": "Add successful", "address_book_add_address.toast_copy_success": "<PERSON>pied", "address_book_add_address.toast_delete_success": "Delete successful", "address_book_add_address.toast_reset_success": "Reset successful", "address_book_add_address.toast_save_success": "Save successful", "address_book_edit_address.button_cancel": "Cancel", "address_book_edit_address.button_confirm": "Confirm", "address_book_edit_address.delete_contact_message": "Please confirm whether to delete this contact from the address book.", "address_book_edit_address.delete_contact_title": "Delete Contact", "address_book_edit_address.title": "Edit Address", "address_book_select.title": "Select contact", "address_copied_toast_title": "{network} address copied", "address_copied_with_type_toast_title": "{network} {addressType} address copied", "address_input.contract_popover": "You are sending to a contract address, not a regular user address. Incorrect transfers to a contract address may result in asset loss. Double-check the address and watch for risks.", "address_input.first_transfer_popover": "First transfer to this address on {network}. Activities on other networks for this address are not tracked. Double-check the address and watch for risks.", "address_input.transferred_popover": "Previous transfers to this address have been detected. Don’t forget to double-check to ensure it is the intended address.", "address_label.allowlist": "Allowlist", "address_type": "Address type", "address_type_selector_cta": "Confirm address", "address_type_selector_desc": "After selecting the address type, the new address will be set as the <strong>default</strong> for transactions.", "address_type_selector_title": "Select address type", "allowlist.enabled_popover_content": "You can only transfer funds to accounts within the wallet or to any address in the address book. If you understand the risks, you can enable it in <url>https://app.onekey.so/send/protection<underline>Settings >> Security >> Protection</underline></url>", "allowlist.enabled_popover_title": "Allowlist enabled", "approval_bulk_revoke_status_paused_reason_description": "Skipped reason", "approval_bulk_revoke_status_processing": "Processing", "approve_edit.approve_amount": "Approve amount", "approve_edit.less_than_swap": "Allowance can't be less than the swap amount", "approve_edit.title": "Edit allowance", "approve_edit.unlimited_amount": "Unlimited amount", "auth.Numeric_Passcode": "6-Digit numeric passcode ", "auth.Passcode_protection": "Enable passcode protection?", "auth.Passcode_protection_description": "Enable passcode protection: after 10 failed passcode attempts will reset App, you can change it anytime on Setting.", "auth.alphanumeric_passcode ": "Alphanumeric passcode ", "auth.biometric_cancel": "User canceled {biometric} authentication", "auth.biometric_failed": "Passcode required to enable {biometric}", "auth.confirm_passcode_form_label": "Confirm passcode", "auth.confirm_passcode_form_placeholder": "Re-enter your passcode", "auth.confirm_password_form_label": "Confirm password", "auth.confirm_password_form_placeholder": "Re-enter your password", "auth.enter_your_passcode": "Enter your passcode", "auth.enter_your_password": "Enter your password", "auth.error_passcode_empty ": "Please enter a passcode", "auth.error_passcode_incorrect": "Incorrect passcode", "auth.error_passcode_not_match": "Passcodes do not match", "auth.error_passcode_too_long": "Passcode cannot exceed {length} characters", "auth.error_passcode_too_short": "Passcode must be at least {length} characters", "auth.error_password_empty": "Please enter a password", "auth.error_password_incorrect": "Incorrect passcode", "auth.error_password_not_match": "Passwords do not match", "auth.error_password_too_long": "Password cannot exceed {length} characters", "auth.error_password_too_short": "Password must be at least {length} characters", "auth.new_passcode_form_label": "New passcode", "auth.new_passcode_form_placeholder": "Create a strong passcode", "auth.new_passcode_same_as_old": "New passcode must differ from the old one", "auth.new_password_form_label": "New password", "auth.new_password_form_placeholder": "Create a strong password", "auth.new_password_same_as_old": "New password must differ from the old one", "auth.passcode_cooldown": "Try again in {cooldowntime} min", "auth.passcode_failed_alert": "{count} more failed attempts will reset App", "auth.passcode_set": "Passcode set", "auth.password_set": "Password set", "auth.set_passcode": "Set passcode", "auth.set_password": "Set password", "auth.with_biometric": "Authentication with {biometric}", "backup.address_book_labels": "Address book & labels", "backup.all_devices": "All devices", "backup.backup_deleted": "Backup deleted", "backup.backup_imported": "Backup imported", "backup.backup_now": "Backup now", "backup.backup_to_google_drive": "Backup to Google Drive", "backup.backup_to_icloud": "Backup to iCloud", "backup.data_already_present": "All the data in this backup is already present on the current device.", "backup.delete_this_backup": "Delete this backup?", "backup.enable_feature_download_google_drive": "To enable this feature, please download Google Drive, log in, and ensure that OneKey has the necessary permissions.", "backup.enable_google_drive": "Enable Google Drive", "backup.enable_icloud": "Enable iCloud", "backup.encrypted_backup_contents": "Encrypted backup contents", "backup.file_permanently_deleted": "This file will be permanently deleted from iCloud. Make sure you have written down the recovery phrases as you won’t be able to restore the wallets otherwise.", "backup.file_permanently_deleted_android": "This file will be permanently deleted from Google Drive. Make sure you have written down the recovery phrases as you won’t be able to restore the wallets otherwise.", "backup.go_system_settings": "Go system settings", "backup.google_backup_sunsetting": "Google Drive backup sunsetting", "backup.google_backup_sunsetting_description": "iCloud backup will be discontinued by the end of 2025. Before then, you can still retrieve your data, but new backups are no longer supported.", "backup.google_drive_auto_backup_paused": "Google Drive auto-backup paused", "backup.google_drive_securely_syncs_data": "Google Drive securely syncs your data across devices (excluding hardware wallets). Please authorize OneKey to access your Google Drive. Neither OneKey nor Google can access your wallets.", "backup.google_drive_status": "Google Drive status", "backup.icloud_auto_backup_paused": "iCloud auto-backup paused", "backup.icloud_backup_securely_syncs_your_data": "iCloud backup securely syncs your data across devices (excluding hardware wallets), ensuring that both OneKey and Apple cannot access your wallets.", "backup.icloud_backup_sunsetting": "iCloud backup sunsetting", "backup.icloud_backup_sunsetting_description": "iCloud backup will be discontinued by the end of 2025. Before then, you can still retrieve your data, but new backups are no longer supported.", "backup.icloud_status": "iCloud Status", "backup.import_data": "Import data", "backup.import_from_google_drive": "Import from Google Drive", "backup.import_from_icloud": "Import from iCloud", "backup.no_available_google_drive_backups_to_import": "You have no available Google Drive backups to import.", "backup.no_available_icloud_backups_to_import": "You have no available iCloud backups to import.", "backup.no_content_available_for_backup": "No content available for backup.", "backup.no_data": "No Data", "backup.number_wallets_number_accounts": "{number0} Wallets · {number1} Accounts", "backup.off_device": "Off-device ({number})", "backup.on_device": "On-device", "backup.onekey_doesnt_back_up_hardware_wallets": "One<PERSON>ey doesn't back up hardware wallets, please record and safeguard your recovery phrase.", "backup.only_accounts_with_addresses_will_be_backed_up": "For the app wallet, only accounts with addresses will be backed up.", "backup.please_log_in_to_your_apple_account_and_activate_icloud_drive": "Please log in to your Apple account and activate the iCloud Drive feature before proceeding.", "backup.please_upgrade_app_to_import_data": "Please upgrade your app to import the data from a newer version.", "backup.recovery_phrase_backed_up": "Recovery phrase backed up", "backup.securely_store_recent_backups": "We'll securely store your most recent 30 daily backups plus the last monthly backup for each of the past 24 months, ready for restoration at any time.", "backup.turn_off": "Turn off", "backup.turn_on": "Turn on", "backup.updated_time": "Updated: {time}", "backup.upgrade_required": "Upgrade required", "backup.verify_app_passcode_to_import_data": "Verify the App passcode at the time of backup to Import data.", "backup.verify_app_password_to_import_data": "Verify the App password at the time of backup to Import data.", "backup.verify_apple_account_and_icloud_drive_enabled": "Please verify your Apple account login and ensure iCloud Drive is enabled and authorized for OneKey.", "backup.verify_google_account_and_google_drive_enabled": "Please verify your Google account login and ensure Google Drive is enabled and authorized for OneKey.", "balance_detail.button_acknowledge": "Acknowledge", "balance_detail.button_balance": "Balance Details", "balance_detail.button_cancel": "Cancel", "balance_detail.button_confirm": "Confirm", "balance_detail.button_done": "Done", "balance_detail.disable_protection_warning_description": "Disabling inscription protection may result in the accidental transfer of inscription tokens or NFTs. To migrate your inscription assets safely, please use an inscription-compatible wallet such as Unisat or OKX.", "balance_detail.disable_protection_warning_title": "Disabling inscription protection may lead to loss of inscription assets", "balance_detail.enable_protection_title": "Enabling Inscription Protection", "balance_detail.enable_protection_warning": "Inscription assets (Ordinals NFT and BRC-20 tokens) are currently not supported. To view the details of the protection, check \"Balance Details\". \nTo migrate your inscription assets safely, please use an inscription-compatible wallet such as Unisat or OKX.", "balance_detail.frozen": "Frozen", "balance_detail.frozen_by_inscription": "Inscription Protection", "balance_detail.how_to_migrate": "How to Migrate Ordinals NFT / BRC-20 Tokens?", "balance_detail.spendable": "Spendable", "balance_detail.total": "Total", "balance_detail.what_is_frozen_balance": "What is Frozen Balance?", "bandwidth_consumed": "Bandwidth consumed", "bandwidth_energy.bandwidth": "Bandwidth", "bandwidth_energy.description": "You can obtain free resources for covering transaction fees by staking TRX on the network. The resources used will dynamically recover to their original amount after a certain period.", "bandwidth_energy.energy": "Energy", "bandwidth_energy.get_resources": "Get Resources", "bandwidth_energy.rent_energy": "Rent Energy", "bandwidth_energy.title": "Bandwidth & Energy", "bandwidth_energy.what_is_bandwidth_energy": "What is Bandwidth & Energy on Tron?", "bip44__standard": "BIP44 standard", "bluetooth.disable_in_settings": "If you prefer using USB only and don’t want to see this prompt again, go to OneKey app > Settings > Preferences and turn off Bluetooth.", "bluetooth.disabled": "Bluetooth is disabled", "bluetooth.enable_in_app_settings": "Please enable Bluetooth in the app settings", "bluetooth.enable_in_system_settings": "Please enable Bluetooth in your system settings", "bluetooth.keep_near": "Keep your device near the computer to pair", "bluetooth.paring_guides_pair": "Enter the code shown on your device and click “Connect.” Unlock if needed", "bluetooth.paring_guides_unlock": "Wait for the pairing request", "bluetooth.paring_guides_wait_for_confirmation": "Wait for the system to confirm and continue", "bluetooth.permission_prompt": "OneKey needs Bluetooth to connect. Please allow it when prompted", "bluetooth_paring_dialog_title": "Pairing with your device…", "browser.bookmark": "Bookmark", "browser.can_be_re_enabled_in_settings": "Can be re-enabled in settings.", "browser.clear_recently_closed": "Clear recently closed?", "browser.clear_recently_closed_description": "Are you sure you want to clear the recently closed tabs?", "browser.copy_link": "Copy link", "browser.dapp_listed_by": "dApp listed by", "browser.disable": "Global Disable", "browser.dive_in": "Let’s Dive in", "browser.dive_in_description": "Seamlessly explore the crypto world and easily connect to your favorite dApps.", "browser.dive_in_mobile": "Browser", "browser.fetching_dapp_info": "Fetching dApp info...", "browser.hide_on_this_site": "Hide on this site", "browser.import": "Import", "browser.invalid_url": "Invalid URL", "browser.last_verified_at": "Last verified at", "browser.no_closed_tabs": "No closed tab yet", "browser.recently_closed": "Recently closed", "browser.request_desktop_site": "Request desktop site", "browser.request_mobile_site": "Request mobile site", "browser.risk_detection": "Risk detection", "browser.risk_detection_unknown": "No risk detected", "browser.search_dapp_or_enter_url": "Search dApps or enter URL", "browser.start_tab": "Start Tab", "browser.switch_to_account": "Switch to {account}?", "clear_build_in_networks_data": "Build-in networks", "coming_soon": "Coming soon", "coming_soon_desc": "We're currently developing this feature and will make it available soon.", "communication.communicating": "Communicating...", "communication.not_detected_bluetooth_fallback": "USB not detected. OneKey will now try Bluetooth — please allow Bluetooth access when prompted.", "communication.not_detected_bluetooth_no_permission": "USB not detected. Please plug in via USB, or grant Bluetooth access to OneKey in your system settings.", "communication.not_detected_bluetooth_not_paired": "USB not detected. Switching to Bluetooth pairing.", "communication.timeout": "Communication timeout", "confirm_exit_dialog_desc": "Are you sure you want to exit the data migration process?", "confirm_exit_dialog_title": "Confirm exit", "contact_us_instruction": "Need more help?", "content__amount": "Amount", "content__custom": "Custom", "content__fast": "Fast", "content__from": "From", "content__gas_limit": "Gas Limit", "content__gas_price": "Gas Price ({network})", "content__normal": "Normal", "content__slow": "Slow", "content__to": "To", "copy_address_modal_item_create_address_instruction": "Create address", "copy_address_modal_title": "Account address", "copy_anyway": "Copy anyway", "copy_recovery_phrases_warning_desc": "Clipboard access can expose your recovery phrase to unauthorized apps.", "copy_recovery_phrases_warning_title": "Copy recovery phrase?", "count_assets": "{count} assets", "count_hidden_assets": "{count} hidden assets", "count_words": "{length} words", "create_qr_based_hidden_wallet_create_hidden_wallet_desc": "After the standard wallet is created, enter a passphrase to make a hidden wallet.", "create_qr_based_hidden_wallet_create_hidden_wallet_title": "Create hidden wallet", "create_qr_based_hidden_wallet_create_standard_wallet_desc": "No passphrase is needed before displaying the QR code. Tap the ✅ button, show the code, and scan it with the app to create a QR-based standard wallet.", "create_qr_based_hidden_wallet_create_standard_wallet_title": "Create standard wallet", "create_qr_based_hidden_wallet_dialog_title": "Create QR-based hidden wallet", "custom_network.add_custom_network_failed_toast_text": "Add custom network failed; please try again.", "custom_network.add_custom_network_successfully_toast_text": "Add custom network successfully", "custom_network.add_network_action_text": "Add custom network", "custom_network.form_alert_text": "Untrusted networks can fake blockchain and track you. Use only trusted ones.", "custom_network.network_exists_feedback_text": "Network already exists", "custom_network.remove_network_dialog_description": "You will have to add it back to access your assets on this network.", "custom_network.remove_network_dialog_title": "Remove this network?", "custom_rpc_cta_label": "Add custom RPC", "custom_rpc_desc": "The custom RPC will replace <PERSON><PERSON><PERSON>’s node. To revert, disable or delete the custom RPC. If not necessary, use OneKey's nodes for optimal service.", "custom_rpc_edit_dialog_title": "Custom {network} RPC", "custom_rpc_empty_title": "No custom RPC", "custom_rpc_title": "Custom RPC", "dapp_connect.allow_this_site_to_access": "Allow this site to access your {chain} address.", "dapp_connect.allow_to_access_your_chain_auth": "Allow this site to access your {chain} auth.", "dapp_connect.allow_to_access_your_chain_decrypted_message": "Allow to access your {chain} decrypted message", "dapp_connect.allow_to_access_your_chain_encrypted_message": "Allow to access your {chain} encrypted message", "dapp_connect.allow_to_access_your_chain_link": "Allow this site to access your {chain} link.", "dapp_connect.allow_to_access_your_chain_login": "Allow this site to access your {chain} login.", "dapp_connect.allow_to_access_your_chain_message_signature": "Allow to access your {chain} message signature.", "dapp_connect.allow_to_access_your_chain_public_key": "Allow to access your {chain} public key.", "dapp_connect.allow_to_access_your_chain_register": "Allow this site to access your {chain} register.", "dapp_connect.amount": "Amount", "dapp_connect.amount_should_be_at_least": "Amount should be at least {0}.", "dapp_connect.amount_should_not_exceed": "Amount should not exceed {0}.", "dapp_connect.attackers_disguise_sites_warning": "Attackers may disguise malicious sites as well-known Web3 sites by replacing with unusual characters. Check URL again before approving any requests.", "dapp_connect.authorization_successful": "Authorization Successful", "dapp_connect.confirm": "Confirm", "dapp_connect.connect_to_website": "Connected to {url}", "dapp_connect.connection_request": "Connection request", "dapp_connect.create": "Create", "dapp_connect.create_invoice_request": "Create Invoice Request", "dapp_connect.decrypted_request": "Decrypted request", "dapp_connect.description_optional": "Description (Optional)", "dapp_connect.detected_malicious_behavior_warning": "Detected malicious behavior on this site by multiple credible sources, it is strongly recommended to reject any request by this site.", "dapp_connect.do_not_ask_again": "Don’t ask again.", "dapp_connect.encrypted_request": "Encrypted request", "dapp_connect.enter_amount": "Enter amount", "dapp_connect.export": "Export", "dapp_connect.export_public_key": "Export Public Key", "dapp_connect.hardware_wallets_do_not_support_solana_message_signing": "Hardware wallets do not support Solana message signing.", "dapp_connect.hide_full_message": "Hide raw message", "dapp_connect.http_connection_not_secure": "Insecure HTTP", "dapp_connect.initiate_message_signature_request": "Initiate message signature request", "dapp_connect.invoice_created": "Invoice created.", "dapp_connect.invoice_description": "Invoice description", "dapp_connect.invoice_payment_request": "Invoice Payment Request", "dapp_connect.link_successful": "<PERSON> Successful", "dapp_connect.lnurl_approve_request": "LNURL Approve Request", "dapp_connect.lnurl_connect_request": "LNURL Connect Request", "dapp_connect.lnurl_login_request": "LNURL Login Request", "dapp_connect.lnurl_pay_request": "LNURL Pay Request", "dapp_connect.lnurl_register_request": "LNURL Register Request", "dapp_connect.lnurl_withdraw_request": "LNURL Withdraw Request", "dapp_connect.login_successful": "Login Successful", "dapp_connect.malicious_site_detected": "Marked as malicious by multiple sources.", "dapp_connect.malicious_site_warning": "Malicious site", "dapp_connect.message": "Message", "dapp_connect.message_to_the_payer": "A message to the payer", "dapp_connect.msg_authentication_failed_verify_again": "Authentication failed, please verify again.", "dapp_connect.msg_description_can_be_up_to_int_characters": "Description can be up to {number} characters.", "dapp_connect.msg_invalid_lightning_payment_request": "Invalid lightning payment request.", "dapp_connect.msg_no_content": "No content.", "dapp_connect.nostr_event_kind_0": "<PERSON><PERSON><PERSON>", "dapp_connect.nostr_event_kind_1": "Short Text Note", "dapp_connect.nostr_event_kind_10002": "Relay List Metadata", "dapp_connect.nostr_event_kind_1984": "Reporting", "dapp_connect.nostr_event_kind_2": "Recommend <PERSON><PERSON>", "dapp_connect.nostr_event_kind_22242": "Client Authentication", "dapp_connect.nostr_event_kind_24133": "Nostr Connect", "dapp_connect.nostr_event_kind_3": "Follows", "dapp_connect.nostr_event_kind_30008": "Profile Badges", "dapp_connect.nostr_event_kind_30009": "Badge Definition", "dapp_connect.nostr_event_kind_30023": "Long-form Content", "dapp_connect.nostr_event_kind_30078": "Application-specific Data", "dapp_connect.nostr_event_kind_4": "Encrypted Direct Messages", "dapp_connect.nostr_event_kind_40": "Channel Creation", "dapp_connect.nostr_event_kind_41": "Channel Metadata", "dapp_connect.nostr_event_kind_42": "Channel Message", "dapp_connect.nostr_event_kind_43": "Channel Hide Message", "dapp_connect.nostr_event_kind_44": "Channel Mute User", "dapp_connect.nostr_event_kind_5": "Event Deletion", "dapp_connect.nostr_event_kind_7": "Reaction", "dapp_connect.nostr_event_kind_8": "Badge Award", "dapp_connect.nostr_event_kind_9734": "Zap Request", "dapp_connect.nostr_event_kind_9735": "Zap", "dapp_connect.nostr_event_kind_unknown": "Nostr {kind} event", "dapp_connect.nostr_plaintext": "Plaintext", "dapp_connect.order_alert": "You are using order authorization, ensure the dApp is trustworthy to avoid asset loss.", "dapp_connect.pay_to_node": "Pay to Lightning Node", "dapp_connect.permit_sign_alert": "You’re using {type} authorization, ensure the dApp is trustworthy to avoid asset loss.", "dapp_connect.proceed_at_my_own_risk": "Proceed at my own risk", "dapp_connect.registration_successful": "Registration Successful", "dapp_connect.request_for_lnurl_linking_key": "Request for LNURL linking key", "dapp_connect.request_invoices_and_lightning_information": "Request invoices and lightning information", "dapp_connect.requested_permissions": "Requested permissions", "dapp_connect.retrieve_your_encryption_public_key": "Retrieve your encryption public key", "dapp_connect.risk_sign": "This type of signature request can sometimes be used for malicious purposes. Only sign it if you fully trust the website.", "dapp_connect.sats_between": "between {min} and {max} {unit}", "dapp_connect.send_approval_requests": "Send approval requests", "dapp_connect.signature_request": "Signature request", "dapp_connect.site_not_using_private_connection_warning": "The site does not use a private connection, attackers may be able to view and change the information you send and get through this site.", "dapp_connect.site_suspected_of_malicious_behavior_warning": "This site is suspected of malicious behavior, continuing to approve requests may pose security risks.", "dapp_connect.suspected_malicious_behavior": "Suspected malicious behavior", "dapp_connect.suspected_malicious_behavior_on_this_site": "Suspected malicious behavior", "dapp_connect.the_sites_connection_is_not_secure": "The site's connection is not secure.", "dapp_connect.url_contains_unusual_characters": "Suspicious URL", "dapp_connect.url_contains_unusual_characters_warning": "URL contains unusual characters.", "dapp_connect.verified_site": "Verified", "dapp_connect.verified_site_warning": "Verified site.", "dapp_connect.verified_web3_site_warning": "Verified Web3 site by multiple credible sources, can be used with confidence.", "dapp_connect.view_decrypted_message": "View decrypted message", "dapp_connect.view_encrypted_message": "View encrypted message", "dapp_connect.view_full_message": "View raw message", "dapp_connect.view_your_balance_and_activity": "View your balance and activity", "dapp_connect.watch_your_account_balance_and_activity": "Watch your account balance and activity", "dapp_connect.withdraw_request_sent": "Withdraw Request Sent", "date.today": "Today", "date.yesterday": "Yesterday", "derivation_path": "Derivation path", "description_403": "Our services are not available in your region.", "device.check_connection": "Ensure device \"{bleName}\" is connected", "device.communication_failed": "Device communication failed. Please try switching to WebUSB mode in \"Settings\" > \"Hardware Communication\" or refer to our <url>https://help.onekey.so/articles/********<underline>OneKey Bridge troubleshooting guide</underline></url> to resolve the connection issue.", "device.communication_failed_with_no_web_usb_supported": "Device communication failed. Please refer to our <url>https://help.onekey.so/articles/********<underline>OneKey Bridge troubleshooting guide</underline></url> to resolve the connection issue.", "device.connect_via_usb": "Connect device via USB", "device.grant_usb_access": "Grant USB Access", "device.hardware_communication": "Hardware communication", "device.not_connected": "Device not connected", "device.reconnect_from_beginning": "Still having issues? <action>reconnect<underline>Reconnect from beginning</underline></action>", "device.select_device_popup": "Click the button below then select your device in the popup to connect.", "device.try_reconnecting_usb": "If not detected, try reconnecting USB", "device_auth.certificate": "Certificate", "device_auth.continue_anyway_warning_message": "We're currently unable to verify your device. Continuing may pose security risks.", "device_auth.request_desc": "Confirm on your device to verify its authenticity and secure your connection.", "device_auth.request_title": "Genuine check", "device_auth.successful_desc": "Your device is now officially verified! You're all set to enjoy a secure and seamless experience.", "device_auth.successful_title": "Verification successful", "device_auth.temporarily_unavailable": "Verification temporarily unavailable", "device_auth.temporarily_unavailable_help_text": "Currently, we're unable to verify your device due to server issues. Please try again later.", "device_auth.unofficial_device_detected": "Unofficial device detected", "device_auth.unofficial_device_detected_help_text": "Your device could not be verified as official. Please contact us immediately.", "device_auth.verifying_component_label": "In progress", "device_auth.verifying_desc": "Please wait...", "device_auth.verifying_title": "Verifying device", "dexmarket.Liq_tips": "Liquidity in this token liquidity pool", "dexmarket.audit": "Audit", "dexmarket.audit_danger": "Danger", "dexmarket.audit_danger_desc": "Trading risk has been detected. Please proceed with caution.", "dexmarket.audit_safe": "Safe", "dexmarket.audit_safe_desc": "No risks detected. This result does not constitute any form of endorsement or recommendation.", "dexmarket.audit_unknown": "Unknown risk", "dexmarket.audit_unknown_desc": "Unable to conduct risk audit for this token. Beware of potential trading risks.", "dexmarket.buy_tap": "Buy", "dexmarket.buy_token_default": "Buy {TokenName}", "dexmarket.buysell_loading": "Finalizing quote...", "dexmarket.circulating_supply_tips": "The amount of this token in circulation at the moment", "dexmarket.connect_wallet": "Connect wallet", "dexmarket.create_address": "Create address", "dexmarket.custom_filters": "Custom filters", "dexmarket.custom_filters_apply": "Apply", "dexmarket.custom_filters_default": "<PERSON><PERSON><PERSON>", "dexmarket.custom_filters_max": "Max", "dexmarket.custom_filters_min": "Min", "dexmarket.custom_filters_period": "Period", "dexmarket.details_audit_issue": "{amount} issues", "dexmarket.details_chart": "Chart", "dexmarket.details_circulating_supply": "Circulating supply", "dexmarket.details_history_amount": "Amount", "dexmarket.details_history_time": "Time", "dexmarket.details_history_type": "Type", "dexmarket.details_history_value": "Value", "dexmarket.details_holders_rank": "Rank", "dexmarket.details_liquidity_change": "Liquidity change", "dexmarket.details_liquidity_change_add": "Add", "dexmarket.details_liquidity_change_remove": "Remove", "dexmarket.details_liquidity_change_total": "Total liquidity", "dexmarket.details_max_supply": "Maximum supply", "dexmarket.details_myposition": "My position", "dexmarket.details_nodata": "No data", "dexmarket.details_overview": "Overview", "dexmarket.details_trade": "Trade", "dexmarket.details_traders_buyers": "Buyers", "dexmarket.details_traders_sellers": "Sellers", "dexmarket.details_transactions": "Transactions", "dexmarket.details_transactions_buy": "Buys", "dexmarket.details_transactions_sell": "Sells", "dexmarket.enter_amount": "Enter token amount", "dexmarket.fdv_desc": "Fully Diluted Valuation - the total value if all tokens were in circulation", "dexmarket.fdv_title": "FDV", "dexmarket.holders": "Holders", "dexmarket.hp_time_filter_1h": "1h", "dexmarket.hp_time_filter_24h": "24h", "dexmarket.hp_time_filter_4h": "4h", "dexmarket.hp_time_filter_5m": "5m", "dexmarket.insufficient_balance": "Insufficient balance", "dexmarket.liquidity": "Liquidity", "dexmarket.market_cap": "Market cap", "dexmarket.max": "Max", "dexmarket.max_supply_tips": "The maximum amount of this token that can circulate in the market. Some tokens do not have a maximum cap, so this data may be inaccurate", "dexmarket.mc_tips": "Market cap = Circulating supply * Price", "dexmarket.mobiletitle_mcap": "Mcap", "dexmarket.price_mc": "Price/Mcap", "dexmarket.select_token": "Select token", "dexmarket.sell_tap": "<PERSON>ll", "dexmarket.sell_token_default": "Sell {TokenName}", "dexmarket.swap_unsupported_desc": "Current network doesn't support swaps. Switch network or wallet.", "dexmarket.swap_unsupported_title": "Swap unsupported", "dexmarket.switch_to_trade": "If you wish to trade other tokens, switch to ", "dexmarket.token_change": "Change", "dexmarket.token_name": "Name", "dexmarket.token_price": "Price", "dexmarket.tokenage": "Token age", "dexmarket.total": "Total", "dexmarket.traders": "Traders", "dexmarket.trending": "Trending", "dexmarket.turnover": "Turnover", "dexmarket.txns": "Txns", "dont_have_mobile_app_yet": "Don’t have the mobile App yet?", "downgrade_warning_checkbox_label": "I will NOT downgrade to OneKey v4", "downgrade_warning_description": "Do NOT downgrade to OneKey v4. Downgrading will lead to irreversible data loss.", "downgrade_warning_title": "Important: Do not downgrade", "earn.24h_earnings": "Est. 24h earnings", "earn.24h_earnings_tooltip": "Estimated 24 hours earnings based on your activated amount.", "earn.active": "Active", "earn.annually": "Annually", "earn.approaching_staking_cap": "As we’re approaching the staking cap, please check your staking results. You’ll need to restake if marked as overflow.", "earn.approve_deposit": "Approve and Deposit", "earn.approve_permit": "Approve (via Permit)", "earn.apy_change_desc": "Since the Fixed APY event has ended, deposits made after the event or above the snapshot limit will earn a variable base APY instead.", "earn.apy_change_title": "New deposits come with a {value} base APY", "earn.auto_risk_control": "Auto risk control", "earn.auto_risk_control_desc_1": "In extreme situations, such as a protocol hack or a price depeg, the vault managers will proactively withdraw investments from the pool.", "earn.auto_risk_control_desc_2": "The funds will remain in the vault’s idle portion for your withdrawal, prioritizing principal safety.", "earn.auto_risk_control_desc_3": "When risk control is triggered, idle funds in the pool increase, leading to lower yields.", "earn.auto_risk_control_disclaimer": "“Auto Risk Control” provide an additional layer of protection for your investment. The timeliness and accuracy of these features rely on third-party support. We will make every effort to ensure normal operation, but there may be technical or operational constraints in certain situations. Therefore, we assume no legal responsibility if “Auto Risk Control” does not function as intended.", "earn.auto_risk_control_subtitle": "Protecting your funds in extreme situations", "earn.automatically": "automatically", "earn.available_assets": "All assets", "earn.available_assets_desc": "Increase your holdings by depositing assets.", "earn.available_to_deposit": "Available to deposit", "earn.banner_stake_in_babylon_ecosystem": "Stake Your BTC with Babylon", "earn.base_apy": "Base APY", "earn.check_staking_results_overflow": "Check your staking results. If marked as overflow, need to be withdrawn and claimed", "earn.claim": "<PERSON><PERSON><PERSON>", "earn.claim_assets_after_processing": "Claim your assets once the processing is complete", "earn.claim_available": "Claim available", "earn.claim_available_in_number_days": "Claim available in {number} days", "earn.claim_limitations": "Due to the limitations of Lido, you must claim each of your withdrawals separately.", "earn.claim_rewards": "Claim rewards", "earn.claim_rewards_morpho_desc": "Rewards are calculated based on all Morpho deposit positions and settled every {time}.", "earn.claim_together_tooltip": "Automatically claimed to your wallet after each deposit or withdrawal request.", "earn.claim_token": "Claim {token}", "earn.claim_token_desc": "Claim your {token} after the withdrawal request has been processed.", "earn.claimable": "Claimable", "earn.claimable_in_future": "{value} {symbol} ({fiatValue}) claimable in future", "earn.claimed": "Claimed", "earn.commission": "Commission", "earn.commission_tooltip": "A specific percentage of your rewards or points is deducted as a commission for the provider.", "earn.confirmed_cap": "Confirmed cap", "earn.currently_staking": "Currently deposited", "earn.daily": "Daily", "earn.day_abbr": "D", "earn.deposit": "<PERSON><PERSON><PERSON><PERSON>", "earn.deposited": "Deposited", "earn.disclaimer": "Disclaimer", "earn.dont_show_again": "Don’t show again", "earn.early_withdraw_stake_unstaking_period": "Early withdraw your stake anytime with an unstaking period of {number} days", "earn.earn_during_unstaking_tooltip": "Assets will continue to earn rewards during the unstaking period after withdrawal.", "earn.earn_points": "Earn points", "earn.earn_points_desc": "OneKey partners with Babylon, enabling you to stake and complete Babylon’s tasks to earn points.", "earn.earn_symbol": "Earn {symbol}", "earn.earn_token": "Earn {token}", "earn.earn_up_to_number_per_year": "Earn up to {number} per year", "earn.earn_up_to_number_per_year_desc": "The APR is updated hourly and adjusted according to changes in TVL.", "earn.earnings_start": "Earnings start", "earn.est_annual_rewards": "Est. annual rewards", "earn.est_daily_rewards": "Est. daily rewards", "earn.est_receive": "Est. receive", "earn.est_receive_tooltip": "These tokens represent your assets and earnings. If you transfer or trade them freely, you might not be able to withdraw your assets.", "earn.event_ends_in": "Event ends in", "earn.falcon_token_airdrop": "Falcon token airdrop", "earn.feature_1_desc": "Deposit your assets and earn passive returns effortlessly", "earn.feature_1_title": "Activate idle assets", "earn.feature_2_desc": "Secure deposits with the freedom to withdraw anytime", "earn.feature_2_title": "Enjoy flexible deposits", "earn.feature_3_desc": "Boost your earnings with optimized deposits at peak profitability", "earn.feature_3_title": "Maximize your rewards", "earn.feature_list_title": "Watch your holdings grow", "earn.finality_provider": "Finality provider", "earn.fixed_apy": "{value} Fixed APY", "earn.fixed_yield_info": "Earn a 35% fixed yield — The base APY portion will be distributed in USDf, and the rest will be covered by Falcon via token airdrops.", "earn.hour_abbr": "H", "earn.how_does_the_lido_protocol_work": "How does the Lido protocol work?", "earn.how_does_the_lido_protocol_work_desc": "Lido provides an innovative solution to the hurdles presented by traditional PoS staking by effectively lowering barriers to entry and the costs associated with locking up one's assets in a single protocol. When a user deposits their assets to Lido, the tokens are staked on the Lido blockchain via the protocol.", "earn.in_days": "in {number} days", "earn.in_number": "In {number}", "earn.insufficient_balance": "Insufficient balance", "earn.insufficient_claimable_balance": "Insufficient claimable balance.", "earn.insufficient_staked_balance": "Insufficient staked balance", "earn.investment_details": "Investment details", "earn.join_requirement": "Join requirement", "earn.last_day": "Last day", "earn.last_month": "Last month", "earn.last_week": "Last week", "earn.lending": "Lending", "earn.less_than_number_days": "< {number} days", "earn.lido_token_redemption": "Lido {token} redemption", "earn.lido_token_staking": "Lido {token} staking", "earn.limited_time_yield": "Limited-time yield", "earn.liquid_staking": "Liquid staking", "earn.manually": "manually", "earn.maximum_staking_alert": "The maximum staking amount is {number} {symbol}.", "earn.min_max_staking": "Min./Max. deposit", "earn.min_staking": "Min. deposit", "earn.min_total_staking": "Min. total staking", "earn.minimum_amount": "The minimum amount is {number}.", "earn.minimum_claim_tooltip": "The minimum reward claim is {number} {symbol}, but rewards will be claimed to your wallet with each staking or withdrawal request, without limits.", "earn.minimum_staking_alert": "The minimum staking amount is {number} {symbol}.", "earn.minimum_total_staking_alert": "The minimum total staking amount must be above {number} {symbol}.", "earn.minimum_withdrawal_alert": "The minimum withdrawal amount is {number} {symbol}.", "earn.minute_abbr": "M", "earn.missing_rewards": "Missing rewards", "earn.missing_rewards_tooltip": "Estimated yearly rewards you could earn by depositing your assets, based on the current value.", "earn.monthly": "Monthly", "earn.native_apy": "Native APY", "earn.native_staking": "Native staking", "earn.native_tokens": "Native tokens", "earn.no_orders": "No orders", "earn.no_orders_desc": "You haven’t staked any assets yet.", "earn.no_protocols_available": "No protocols available", "earn.not_enough_token": "Not enough {token}?", "earn.number_day": "{number} day", "earn.number_days": "{number} day(s)", "earn.number_days_left": "{number} days left", "earn.number_days_number_block": "{number_days} days ({number} block)", "earn.number_hours": "{number} hours", "earn.number_minutes": "{number} minutes", "earn.number_seconds": "{number} seconds", "earn.number_symbol_left": "{number} {symbol} left", "earn.overflow": "Overflow", "earn.overflow_alert": "Overflow stake should be withdrawn and claimed.", "earn.overflow_number_alert": "{number} BTC is in overflow and should be withdrawn and claimed.", "earn.pay_with": "Pay with", "earn.pending_activation": "Pending activation", "earn.pending_activation_tooltip": "New staking needs to wait around {number} before becoming active and starting to earn rewards.", "earn.pending_activation_tooltip_eth": "Your staked ETH becomes active once the validator reaches the 32 ETH requirement.", "earn.pending_transactions_data_out_of_sync": "Data may take time to sync after the successful transaction.", "earn.performance_fee": "Performance fee", "earn.performance_fee_desc": "Vault managers work to boost your returns and reduce your risks. In exchange, a certain percentage of your earnings or points is taken as a performance fee.", "earn.period": "Period", "earn.period_before_date": "Before {date}", "earn.period_ends_on_date": "Ends on {date}", "earn.period_label_1": "Opt in and deposit your USDf:", "earn.period_label_2": "Base yield distribution:", "earn.period_label_3": "Falcon token airdrop distribution:", "earn.period_label_4": "Guaranteed yield period:", "earn.period_real_time_update": "Real-time update", "earn.period_tge_time": "TGE time (announced by Falcon)", "earn.portfolio": "Portfolio", "earn.portfolio_details": "Portfolio details", "earn.protection": "Protection", "earn.protocol_rewards": "Protocol rewards", "earn.provider_asset_staking": "{provider} {asset} staking", "earn.provider_asset_withdrawal": "{provider} {asset} withdrawal", "earn.provider_staked": "Provider staked", "earn.reaching_staking_cap": "Staking is temporarily unavailable due to hitting the cap or passing the deadline.", "earn.receive": "Receive", "earn.receive_lido_nft": "Receive Lido NFT", "earn.receive_lido_nft_desc": "Each withdrawal request generates a Lido NFT, and its appearance changes when your {token} becomes available for withdrawal.", "earn.receive_reward": "Receive reward", "earn.receive_steth_desc": "When you stake ETH you receive 1:1 stETH. You can unstake and trade this liquid asset at any time.", "earn.receive_stmatic_desc": "When you stake MATIC you receive stMATIC. You can unstake and trade this liquid asset at any time.", "earn.receive_token": "Receive {token}", "earn.receive_token_trade_anytime": "Receive {token} and trade it at any time", "earn.recommended": "Recommended", "earn.redeem": "Redeem", "earn.referral_add_invite_code": "Add referral code", "earn.referral_already_linked_subtitle": "Switch to an available address to link.", "earn.referral_already_linked_title": "This address is already linked to a referral code", "earn.referral_bonus": "Referral bonus", "earn.referral_change_invite_code_title": "Change referral code", "earn.referral_enter_invite_code_note": "After the address is bound to the referral code, it can’t be changed.", "earn.referral_enter_invite_code_subtitle": "Use a referral code to receive yield boost in supported vaults", "earn.referral_enter_invite_code_title": "Enter referral code", "earn.referral_for_you_reward": "Unlock lifetime rewards from your friends’ 10% fee sharing", "earn.referral_for_your_friend_reward": "Get up to {number} yield boost", "earn.referral_referral_reward": "Referral rewards", "earn.referral_subtitle": "Invite friends to bind your referral code when creating their wallet.", "earn.referral_title": "Referral and earn more!", "earn.referral_total_earned": "Total earned", "earn.referral_undistributed": "Undistributed", "earn.referral_unlinked": "Unlinked", "earn.referral_view_rewards": "View rewards", "earn.referral_your_referral_link": "Your referral link", "earn.register": "Opt in", "earn.remaining_to_minimum": "Still {value} {symbol} to reach the minimum join requirement", "earn.request_withdrawal": "Request withdrawal", "earn.request_withdrawal_steth_desc": "Lock your stETH/wstETH by issuing a withdrawal request. After 1-4 days, the locked stETH will be destroyed and your ETH will become available for withdrawal.", "earn.request_withdrawal_stmatic_desc": "1-4 days after issuing a withdrawal request, the locked stMATIC will be destroyed and your MATIC will become available for withdrawal.", "earn.reward_tokens": "Reward tokens", "earn.reward_value": "Reward value", "earn.rewards": "Rewards", "earn.rewards_automatically_restaked": "Rewards are {automatically} restaked, increasing your staked body", "earn.rewards_dynamic_real_time": "Rewards are dynamic and update in real time based on market supply and demand", "earn.rewards_manually_restaked": "Rewards need to be {manually} withdrawn and restaked", "earn.rewards_percentage": "Rewards (%)", "earn.rewards_updated_around_time": "Rewards updated around {time}", "earn.rewards_updated_daily": "Rewards updated {daily}", "earn.rewards_updated_daily_steth_desc": "There’ll be a daily update on your stETH balances, which includes the staking rewards.", "earn.rewards_updated_daily_stmatic_desc": "During the staking period, the value of stMATIC changes to reflect earnings.", "earn.select_a_claimable_order": "Select a claimable order", "earn.select_an_order_to_withdraw": "Select an order to withdraw", "earn.select_for_early_withdrawal": "Select for early withdrawal", "earn.select_for_early_withdrawal_desc": "Unstake your assets before their expiration", "earn.share_earn_with_friends": "Share this DeFi opportunity with friends", "earn.stablecoins": "Stablecoins", "earn.stake": "Stake", "earn.stake_and_earn": "Deposit and earn", "earn.stake_in_babylon_ecosystem": "Stake in Babylon ecosystem", "earn.stake_release_period": "Stake release period", "earn.stake_release_period_desc": "After issuing a withdrawal request, you'll get an NFT certificate to claim funds. It'll take about 1-4 days before you can claim.", "earn.stake_through_onekey_earn_points": "Stake through OneKey and earn the same points as on the official website", "earn.stake_token": "Stake {token}", "earn.staked": "Staked", "earn.staked_assets_available_after_period": "After the above time period, then your deposited assets will be available to claim.", "earn.staked_value": "Deposits", "earn.staking_cap": "Staking cap", "earn.staking_methods": "Deposit types", "earn.supply": "Supply", "earn.symbol_staking_provider": "{symbol} provider", "earn.term": "Term", "earn.term_number_block": "({number} block)", "earn.term_number_days": "{number_days} days", "earn.term_tooltip": "BTC can be claimed at the end of the staking term.", "earn.token_available_to_stake": "{token} available to stake", "earn.token_is_claimable": "{token} is claimable", "earn.token_is_pending": "{token} is pending", "earn.token_is_staked": "{token} is staked", "earn.total_apy": "Total APY", "earn.total_staked_value": "Total deposits", "earn.transaction_loss": "Transaction loss", "earn.transaction_loss_when_claim": "Due to the est network fee exceeding your rewards, this transaction will result in a loss of {number}.", "earn.transaction_loss_when_stake": "Based on the current estimated rate, it will take about {number} for your earnings to cover the losses.", "earn.tvl": "TVL", "earn.unlock_time": "Unlock time", "earn.unstake_all_due_to_min_withdrawal": "Due to the minimum withdrawal amount of {number} {symbol}, this transaction will unstake all assets to avoid insufficient funds for the next withdrawal.", "earn.unstaking_period": "Unstaking period", "earn.unstaking_period_tooltip": "The estimated period from requesting withdrawal to when tokens can be claimed.", "earn.unsupported_path_desc": "Switch the derivation path to Taproot", "earn.unsupported_path_title": "This stake doesn’t support {path}", "earn.unsupported_token": "Unsupported token", "earn.unsupported_token_desc": "This token is not currently supported for Buy service.", "earn.until_next_launch": "Until next launch", "earn.until_next_launch_tooltip": "A minimum of 32 ETH is required for a pool to begin producing rewards for users.", "earn.up_to_number_days": "Up to {number} days", "earn.up_to_number_in_annual_rewards": "Up to {number} in annual rewards", "earn.update_frequency": "Update frequency", "earn.updated_daily": "Updated daily", "earn.usdf_risk_desc": "It is recommended to read the Falcon documentation and evaluate the protocol’s security and liquidity risks independently.", "earn.usdf_risk_title": "USDf is managed by Falcon", "earn.validator": "Validator", "earn.vault": "<PERSON><PERSON>", "earn.vault_manager": "Vault manager", "earn.wallet_not_support_stake": "The connected wallet do not support stake. Try switch to another one.", "earn.weekly": "Weekly", "earn.what_is_lending": "What is lending?", "earn.what_is_lending_desc": "Lending lets you earn passive income by supplying assets to a lending protocol’s liquidity pool. Borrowers pay interest on borrowed funds, and you receive a portion as interest, growing your holdings over time.", "earn.what_is_liquid_staking": "What is liquid staking?", "earn.what_is_liquid_staking_desc": "Liquid staking lets you earn rewards while keeping your tokens liquid. You can trade or lend them, giving you more flexibility to explore other DeFi opportunities for higher returns.", "earn.what_is_native_staking": "What is native staking?", "earn.what_is_native_staking_desc": "Native staking allows you to earn passive income by locking your tokens to secure a Proof-of-Stake blockchain. In return, you receive rewards in the same tokens, offering a low-risk way to grow your assets.", "earn.what_is_the_possible_risk_of_lido": "What is the possible risk of Lido?", "earn.what_is_the_possible_risk_of_lido_desc": "There is a certain risk in using Lido for staking, such as network or validator failures that may result in the loss of staked assets (penalties), or Lido smart contract vulnerabilities or errors. Although the code has been open-sourced, audited and widely covered, any cryptocurrency investment carries risks and needs to be evaluated independently.", "earn.why_do_you_receive_steth": "Why do you receive stETH?", "earn.why_do_you_receive_steth_desc": "When you deposit ETH into Lido, you receive Lido's liquid staking token, stETH, which represents your proportional claim to ETH in Lido. As validators operating on Lido receive rewards, you are eligible to receive rewards proportional to your stake, which is typically expected to occur daily.", "earn.why_do_you_receive_stmatic": "Why do you receive stMATIC?", "earn.why_do_you_receive_stmatic_desc": "When you deposit MATIC into Lido, you receive Lido's liquid staking token, stMATIC, which represents your proportional claim to MATIC in Lido. As validators operating on Lido receive rewards, you are eligible to receive rewards proportional to your stake, which is typically expected to occur daily.", "earn.withdraw_token": "Withdraw {token}", "earn.withdrawal_only": "<PERSON><PERSON><PERSON> only", "earn.withdrawal_process_desc": "The withdrawal process is simple and will be divided into the following steps:", "earn.withdrawal_processed_immediately": "Withdrawal will be processed immediately, and your staked assets will be returned right away.", "earn.withdrawal_requested": "<PERSON><PERSON><PERSON> requested", "earn.withdrawal_take_up_to_number_days": "Take {number} days after issuing a withdrawal request", "earn.withdrawal_up_to_number_days": "Withdrawal can take up to {number} days, and then your staked assets will be available", "earn.withdrawn": "Withdrawn", "earn_reward_distribution_schedule": "Rewards will be distributed to your Arbitrum wallet (same address as Ethereum) by the 10th of next month.", "edit_fee_custom_set_as_default_description": "Set as default for all future transactions on {network}", "energy_consumed": "Energy consumed", "enter_passcode": "Enter passcode", "enter_password": "Enter password", "enter_pin.desc": "Check device screen for keypad layout.", "enter_pin.enter_on_device": "Enter PIN on device", "enter_pin.invalid_pin": "Invalid PIN code", "enter_pin.title": "Enter PIN code", "enter_pin_on_app": "Enter PIN on app", "explore.add_bookmark": "Add Bookmark", "explore.add_to_whitelist": "add to whitelist", "explore.addresses_count": "{number} addresses", "explore.all_chains": "All Chains", "explore.back_to_home": "Back to Home", "explore.badge_airdrop": "Airdrop", "explore.badge_hot": "Hot", "explore.badge_new": "New", "explore.bookmark_at_least": "Bookmark must be at least 1 characters", "explore.bookmark_exceed": "Bookmark cannot exceed 24 characters", "explore.bookmark_renamed": "Bookmark Renamed", "explore.bookmarks": "Bookmarks", "explore.camera_permission": "Camera: For taking photos or video calls.", "explore.cancel_default": "Cancel the default on this dApp", "explore.categories": "Categories", "explore.category_exchanges": "Exchanges", "explore.category_games": "Games", "explore.category_marketplaces": "Marketplaces", "explore.category_new": "New", "explore.clear_history_message": "Are you sure you want to clear your browser history?", "explore.clear_history_prompt": "Clear history?", "explore.close_all": "Close all", "explore.close_pin_tab": "Close Pin Tab", "explore.close_tab": "Close Tab", "explore.connected_accounts": "Connected Accounts", "explore.connection_is_not_private": "Connection is not private", "explore.connection_is_not_private_warning": "Only supports HTTPS protocol, unsafe website is vulnerable to attacks and forgerly.", "explore.dapp_connections": "dApp connections", "explore.date_format_short": "{month_abbr} {day} {year}", "explore.date_today": "Today", "explore.date_yesterday": "Yesterday", "explore.default_wallet_canceled": "<PERSON><PERSON><PERSON> Canceled", "explore.default_wallet_canceled_desc": "Refresh the page to retry with a different wallet.", "explore.default_wallet_set": "One<PERSON>ey is your default wallet now.", "explore.default_wallet_settings": "De<PERSON>ult Wallet Settings", "explore.disconnect": "Disconnect", "explore.dismiss": "<PERSON><PERSON><PERSON>", "explore.enter_bookmark_name": "Please enter the bookmark name", "explore.excluded_dapps": "Excluded dApps", "explore.excluded_dapps_description": "Right-click blank space, select the option below to exclude.", "explore.explore": "Explore", "explore.got_it": "Got it!", "explore.history": "History", "explore.location_permission": "Location: For providing location-based services.", "explore.malicious_dapp": "Malicious dApp", "explore.malicious_dapp_warning": "The current website may be malicious, continue visiting could result in loss of assets. If you understand the risks and want to proceed, you can dismiss or add to whitelist.", "explore.malicious_dapp_warning_addToWhiteListLink": "add to whitelist", "explore.malicious_dapp_warning_continueLink": "dismiss", "explore.malicious_dapp_warning_continueMessage": "If you understand the risks and want to proceed, you can", "explore.malicious_dapp_warning_description": "The current website may be malicious. Continue visiting could result in loss of assets.", "explore.malicious_dapp_warning_sourceMessage": "Powered by", "explore.manage_dapp_connections": "Manage dApp connections", "explore.microphone_permission": "Microphone: For voice input or calls.", "explore.network_issue_detected": "Network issue detected. Please verify your connection and refresh the page.", "explore.new_tab": "New Tab", "explore.no_bookmark": "No Bookmarks Yet", "explore.no_dapps_connected": "No dApps connected", "explore.no_dapps_connected_message": "You haven't connected to any dApps yet.", "explore.no_history": "No History Yet", "explore.open_in_browser": "Open in Browser", "explore.options": "Options", "explore.permission_restriction_alert": "Permission restriction alert", "explore.permission_restriction_message": "For your safety, the following permissions will be disabled on this site:", "explore.pin": "<PERSON>n", "explore.refresh_page": "Please refresh the page and retry", "explore.refresh_page_link": "refresh the page", "explore.reload": "Reload", "explore.remove_all": "Remove all", "explore.remove_bookmark": "Remove Bookmark", "explore.removed_success": "Removed successfully", "explore.rename": "<PERSON><PERSON>", "explore.risky_domain": "Risky domain", "explore.risky_domain_warning": "Possibly a fake website. Attackers sometimes make subtle, undetectable changes to URLs to impersonate websites.", "explore.search_dapps": "Search dApps", "explore.search_placeholder": "Search", "explore.see_all": "See all", "explore.set_default": "<PERSON> as <PERSON><PERSON><PERSON>", "explore.set_default_wallet": "Set OneKey as default wallet", "explore.set_default_wallet_description": "Use OneKey as the default wallet to connect to dApps.", "explore.share": "Share", "explore.suggested": "Suggested", "explore.tab_prompt": "You can touch and hold to pin this tab for quick access.", "explore.tabs_count": "{number} Tabs", "explore.toast_bookmark_added": "Bookmark added", "explore.toast_bookmark_removed": "Bookmark removed", "explore.toast_pinned": "Pinned", "explore.toast_tab_limit_reached": "<PERSON><PERSON> has reached the maximum limit of {number}.", "explore.toast_unpinned": "Unpinned", "explore.unable_to_connect": "Unable to connect", "explore.unpin": "Unpin", "explore.unsupported_chain": "Unsupported yet", "extension.disk_full": "Low disk space", "extension.disk_full_desc": "Some app features have been disabled due to low disk space. You won’t be able to sign transactions, manage wallets and accounts, and perform other tasks. Please free up space and restart the app to restore full functionality.", "faq.private_key": "What is a private key?", "faq.private_key_desc": "A unique alphanumeric code to control your assets.", "faq.private_key_keep": "Keep your private key safe", "faq.private_key_keep_desc": "Never disclose this key. Anyone with your private keys can steal any assets held in your account.", "faq.recovery_phrase": "What is a recovery phrase?", "faq.recovery_phrase_explaination": "A series of 12, 18, or 24 words to restore your wallet.", "faq.recovery_phrase_safe_store": "Is it safe to enter it into OneKey?", "faq.recovery_phrase_safe_store_desc": "Yes, it’s stored locally and never leave your device without your explicit permission.", "faq.watched_account": "Watch-Only account", "faq.watched_account_desc": "Watch-Only wallet in OneKey allows monitoring of a specific address but cannot send or receive funds. It's useful for tracking transactions or monitoring holdings.", "fee.expected_fee": "Expected fee", "fee.fee": "Fee", "fee.fee_rate": "Fee rate", "fee.fee_rate_too_high": "{something} is higher than necessary", "fee.fee_rate_too_low": "{feeParam} is low for current network conditions", "fee.l1_base_fee": "L1 base fee", "fee.max_fee": "Max fee", "fee.new_fee": "New fee", "fee.original_fee": "Original fee", "fee_alert_dialog_checkbox_label": "I still want to proceed with the transaction.", "fee_alert_dialog_description": "The network fee is currently too high. Please reduce the fee or try again later.", "fee_alert_dialog_title": "Network fee alert", "feedback.account_balance_not_equal_to_utxos": "Available balance cannot be less than 0, input amount: {amount}, dust: {dust}", "feedback.address_mismatch": "Address mismatch", "feedback.address_mismatch_desc": "The address doesn’t match the hardware wallet. Stop using this address immediately and contact us.", "feedback.address_not_activated_message": "Address not activated yet", "feedback.address_not_matched": "Address not matched", "feedback.address_pasted_text": "Address pasted", "feedback.address_type_does_not_support_sign_method": "Only {type} addresses support this signature method", "feedback.bluetooth_issue": "Bluetooth issue", "feedback.bluetooth_pairing_failed": "Bluetooth pairing failed", "feedback.bluetooth_unpaired": "Bluetooth unpaired", "feedback.chainid_unsupported_yet": "{chainId} unsupported yet", "feedback.change_saved": "Change saved", "feedback.connected_accounts_speed_up_or_cancel": "The connected account cannot alter a transaction. To override, submit a new transaction with the same nonce via your external wallet.", "feedback.connection_request_denied": "Connection request denied", "feedback.copied": "<PERSON>pied", "feedback.current_network_message": "{network} is the current network", "feedback.dapp_connected_account": "{dapp} connected {account}", "feedback.derivation_path_restriction": "Derivation path restriction", "feedback.device_psbt_signature_utxo_reached_limit_desc": "The number of UTXOs exceeds the device limit of {count} UTXOs per signature for this device. You can try to send required funds to this address first to create a single UTXO, then try again.", "feedback.device_psbt_signature_utxo_reached_limit_title": "PSBT Signature Failed - UTXO Limit Exceeded", "feedback.edited_network_fee_still_too_low_message": "The edited network costs were less than required, so we’ve adjusted them automatically for you.", "feedback.error_dot_account_retention_prompt": "Transfers may reduce the balance below {0}, resulting in account deactivation and clearing balance due to Polkadot on-chain mechanism. Confirm before proceeding.", "feedback.external_wallet_does_not_approve_address": "The external wallet does not approve this address", "feedback.external_wallet_does_not_approve_network": "The external wallet does not approve this network", "feedback.failed_to_fetch_fee_rate": "Failed to get fee rate, please try again", "feedback.failed_to_fetch_network_fee": "Failed to fetch network fee", "feedback.failed_to_get_utxos": "No available UTXO. Please wait or try a smaller amount.", "feedback.failed_to_parse_transaction": "Failed to parse transaction", "feedback.failed_to_sign_transaction": "Failed to sign transaction", "feedback.forbidden_key_path_error": "You may encounter a forbidden key path error. Disable hardware “Security Check” to resolve.", "feedback.hardware_is_busy": "Hardware is currently busy. Please try again later", "feedback.hardware_unsupported_current_address_type": "This device does not support this type of address", "feedback.hw_create_unsupported_address_title": "Failed to create {network} {addressType} address", "feedback.hw_polling_time_out": "Connection timeout", "feedback.invalid_phrases": "Invalid phrases", "feedback.invalid_qr_code": "Invalid QR code", "feedback.invalid_words_title": "Invalid words", "feedback.invalid_words_title_message": "Double-check and retry", "feedback.kaspa_utxo_limit_exceeded_text": "Transfer failed. UTXO limit exceeded. Please consolidate UTXOs or reduce transfer amount to {amount} {symbol}.", "feedback.network_hidden_from_all_networks_toast_title": "{network} assets now hidden from 'All networks' view", "feedback.network_shown_in_all_networks_toast_title": "{network} assets now shown in 'All networks' view", "feedback.no_connected_account": "No connected account", "feedback.onekey_bridge_installation_required": "Please install OneKey Bridge first. OneKey Bridge facilitates seamless communication between OneKey and your browser, ensuring an enhanced user experience.", "feedback.passcode_set_failed": "Passcode setup failed", "feedback.passphrase_disabled": "Passphrase disabled", "feedback.passphrase_not_matched": "Passphrase not matched", "feedback.passphrase_verification_cancelled": "Passphrase verification cancelled", "feedback.password_set_failed": "Password setup failed", "feedback.pasted_and_cleared": "Pasted and clipboard cleared", "feedback.pin_verification_cancelled": "PIN verification cancelled", "feedback.polkadot_supported_recover_phrases_type": "Only wallets with 12/24-word recovery phrases can create addresses for this network", "feedback.psbt_inputs_mismatch": "PSBT input mismatch detected", "feedback.psbt_not_found": "PSBT could not be located", "feedback.psbt_outputs_mismatch": "PSBT output mismatch detected", "feedback.psbt_uuid_mismatch": "PSBT UUID mismatch detected", "feedback.remove": "Removed", "feedback.request_failed": "Request failed", "feedback.risk_detection_timed_out": "Risk detection information retrieval failed.", "feedback.sign_success": "Signed successfully", "feedback.sol_sign_unupported_message": "Signing Solana message is not supported yet", "feedback.transaction_ckb_error_convert": "Failed to convert transaction, please try again.", "feedback.transaction_ckb_error_less": "The balance after the transaction must not be less than {miniAmount}.", "feedback.transaction_submitted": "Transaction submitted", "feedback.transfer_cause_balance_lower_1_dot": "The balance will be below {amount} {symbol} after the transfer, and the remaining balance will be cleared", "feedback.try_repairing_device_in_settings": "Try re-pairing the device in settings.", "feedback.try_toggling_bluetooth": "Try toggling Bluetooth off and on.", "feedback.unable_to_send_frozen_balance": "Unable to send frozen balance. Please check the Balance Details.", "feedback.unsupported_address_or_network": "Unsupported address or network: {routeAddress}", "feedback.unsupported_chains": "Unsupported chains", "feedback.unsupported_methods": "Unsupported methods", "feedback.user_rejected": "User rejected", "feedback.wallet_exists_desc": "Switched to existing wallet", "feedback.wallet_exists_title": "Wallet exists", "feedback.wallet_exsited_due_to_same_pin_desc": "A hidden wallet is already associated with the current PIN and has been selected for you. To create a new hidden wallet, please unlock the device with a new PIN.", "feedback.walletconnect_session_disconnected": "WalletConnect session disconnected", "feedback.you_are_offline": "You are offline. Please check your network.", "firmware_update.changelog_introduction": "New firmware update available with the following improvements:", "firmware_update.error_connection": "Check your internet connection and try again later.", "firmware_update.error_transfer_interrupted": "Transfer interrupted. Please keep your device connected and try again.", "firmware_update.grant_usb_instruction": "The device is in Bootloader mode. Click the button below to grant USB access to proceed with the installation package transfer.", "firmware_update.status_completed": "Completed", "firmware_update.status_transferring_data": "Transferring data", "firmware_update.status_validating": "Validating", "for_reference_only": "Reference only", "form.address_error_invalid": "Invalid address", "form.address_placeholder": "Address or domain", "form.amount_placeholder": "Enter amount", "form.amount_recipient_activate": "Recipient requires reserve {amount} {unit} to activate", "form.block_explorer_url_label": "Block explorer URL", "form.confirm_passphrase": "Confirm passphrase", "form.confirm_passphrase_placeholder": "Re-enter your passphrase", "form.custom_rpc_error_invalid": "Invalid RPC", "form.enter_account_name": "Name (optional)", "form.enter_account_name_placeholder": "Account name", "form.enter_private_key_placeholder": "Enter your private key", "form.fee_rate_error_out_of_range": "Fee rate must be between {min} and {max}", "form.gas_limit_error_range": "Gas limit must between {min} and {max}", "form.global_error_something_higher_then_necessary": "{something} is higher than necessary", "form.keep_hidden_wallet_label": "Keep accessible", "form.keep_hidden_wallet_label_desc": "Hidden wallets clear on app close. Toggle to preserve.", "form.lightning_invoice_amount_error_max": "Amount must not exceed {amount} {unit}", "form.lightning_invoice_error_positive_integer_only": "Only positive integers allowed", "form.lightning_invoice_placeholder": "e.g., Coffee purchase, Invoice #12345", "form.max_base_fee_description": "Base fee required", "form.must_greater_then_value": "Must be greater than {value}", "form.network_name_label": "Network name", "form.optional_indicator": "Optional", "form.payment_id_error_text": "Payment ID must be a 64-char hex string", "form.private_key_error_invalid": "Invalid private key", "form.mnemonic_error_invalid": "Invalid recovery phrase", "form.invalid_input": "Invalid input", "form.required": "This field is required", "form.max_length": "Maximum {length} characters allowed", "form.public_key_error_invalid": "Invalid public key", "form.public_key_placeholder": "Enter your public key", "form.public_key_title": "Public key", "form.recipient_ln_placeholder": "Enter invoice, Lighting Address or LNURL", "form.rename_error_empty": "Name is required", "form.rename_error_exist": "The name already exists", "form.rpc_url_invalid": "Invalid URL", "form.rpc_url_prefix_required": "http/https prefix required", "form.search_address_placeholder": "Search address", "form.search_network_placeholder": "Search network", "form.symbol_recommend_text": "Chain ID {chainID} usually uses {symbol}. Check the symbol entered.", "form__approve_str": "Approve {amount} {symbol}", "form__priority_fee": "Priority fee", "form__sats__units": "sats", "global.404_message": "Sorry, something went wrong!", "global.Note": "Note", "global.about": "About", "global.about_device": "About device", "global.about_qr_details_answer_a": "Only the name of the QR-based wallet can be edited.", "global.about_qr_details_answer_b": "If you need to check, set the device parameters, or upgrade the firmware, please connect via USB or Bluetooth.", "global.about_qr_details_question_a": "What's customizable in QR-based wallet?", "global.about_qr_details_question_b": "Where to check more device info?", "global.account": "Account", "global.account_name": "Account name", "global.accounts": "Accounts", "global.acknowledged": "Acknowledged", "global.action_verify_and_export": "Verify & Export", "global.add": "Add", "global.add_account": "Add account", "global.add_hidden_wallet": "Add hidden wallet", "global.add_new_device": "Add new device", "global.add_wallet": "Add wallet", "global.address": "Address", "global.advance": "Advance", "global.advanced": "Advanced", "global.advanced_settings": "Advanced settings", "global.advantage_settings": "Advanced settings", "global.age": "Age", "global.all": "All", "global.all_networks": "All networks", "global.allow": "Allow", "global.always": "Always", "global.an_error_occurred": "An error occurred", "global.an_error_occurred_desc": "We’re unable to complete your request. Please reload the page.", "global.app_wallet": "App wallet", "global.apply": "Apply", "global.approval": "Approval", "global.approval_time": "Approval time", "global.approvals": "Approvals", "global.approve": "Approve", "global.apr": "APR", "global.asset": "<PERSON><PERSON>", "global.auto": "Auto", "global.available": "Available", "global.backed_up": "Backed up", "global.backup": "Backup", "global.backup_recovery_phrase": "Backup recovery phrase", "global.balance": "Balance", "global.bandwidth": "Bandwidth", "global.best": "Best", "global.biometric": "Biometric", "global.biometric_disabled": "{authentication} is temporarily disabled", "global.biometric_disabled_desc": "Please re-enable {authentication} manually due to a detected device change.", "global.block_explorer": "Block explorer", "global.block_height": "Block height", "global.bluetooth": "Bluetooth", "global.bluetooth_firmware": "Bluetooth firmware", "global.bootloader": "Bootloader", "global.browser": "Browser", "global.bulk": "Bulk", "global.bulk_accounts_advance": "Advance", "global.bulk_accounts_loading": "{amount} address(es) added", "global.bulk_accounts_loading_error": "Bulk add accounts have been canceled", "global.bulk_accounts_loading_subtitle": "({amount} accounts)", "global.bulk_accounts_page_number": "Page number", "global.bulk_accounts_page_number_error": "Invalid number", "global.bulk_accounts_preview": "Preview", "global.bulk_add_account_dnx_error": "Bulk add accounts error, please connect OneKey Classic", "global.bulk_add_accounts": "Bulk add accounts", "global.bulk_add_accounts_error": "You can add a maximum of {amount} accounts", "global.bulk_copy_addresses": "Bulk copy addresses", "global.bulk_copy_addresses_action_export_without_device": "Export without device", "global.bulk_copy_addresses_addresses_copied": "Addresses copied", "global.bulk_copy_addresses_checking_device_status": "Checking device & awaiting address from hardware", "global.bulk_copy_addresses_export_csv": "Export CSV", "global.bulk_copy_addresses_loading_error": "Bulk copy addresses have been canceled", "global.bulk_copy_addresses_no_wallet_description": "This wallet hasn't been created yet. Please create it first to copy its addresses, or you can proceed to copy from an existing wallet.", "global.bulk_copy_addresses_no_wallet_main_button": "Create wallet", "global.bulk_copy_addresses_no_wallet_secondary_button": "Copy existing", "global.bulk_copy_addresses_no_wallet_title": "Create a wallet first", "global.bulk_copy_addresses_tabs_my_accounts": "My accounts", "global.bulk_copy_addresses_tabs_set_range": "Set range", "global.bulk_revoke": "Bulk revoke", "global.bulk_revoke_desc": "Safeguard your assets by revoking multiple dApp approvals in bulk.", "global.buy": "Buy", "global.buy_one": "Buy one", "global.cancel": "Cancel", "global.cancel_confirm_on_device_feedback": "Device unconfirmed", "global.cancel_copy": "Cancel copy", "global.cancelling": "Cancelling", "global.cash_out": "Cash out", "global.certifications": "Certifications", "global.chain": "Chain", "global.change_passcode": "Change passcode", "global.change_password": "Change password", "global.change_wallpaper": "Change Wallpaper", "global.check_for_updates": "Check for updates", "global.check_it_out": "Check it out", "global.checking_device": "Checking device", "global.circulating_supply": "Circulating supply", "global.clear": "Clear", "global.client_side": "Client-side", "global.close": "Close", "global.close_confirm_description": "You have unsaved changes. If you close now, this information will be lost.", "global.collapse": "Collapse", "global.collect_to_device": "Collect to device", "global.collect_to_device_canceled": "Canceled on device", "global.collect_to_device_failed": "Failed to collect, please try again", "global.community": "Community", "global.confirm": "Confirm", "global.confirm_on_device": "Confirm on device", "global.confirmations": "Confirmations", "global.connect": "Connect", "global.connect_hardware_wallet": "Connect hardware wallet", "global.connect_to_wallet": "Connect to {wallet}", "global.connect_to_wallet_confirm_to_proceed": "Confirm on your wallet to proceed", "global.connect_to_wallet_no_confirmation": "We didn’t receive the confirmation. Please try again.", "global.connect_wallet": "Connect wallet", "global.connected": "Connected", "global.connected_account": "Connected", "global.connection_failed": "Connection failed", "global.connection_failed_help_text": "Please reconnect the device and try again", "global.connection_failed_usb_help_text": "Please reconnect the USB and try again", "global.connet_error_try_again": "Connection error, please try again", "global.contact": "Contact", "global.contact_us": "Contact us", "global.continue": "Continue", "global.continue_anyway": "Continue anyway", "global.contract": "Contract", "global.contract_address": "Contract address", "global.contract_call": "Contract call", "global.copied": "<PERSON>pied", "global.copy": "Copy", "global.copy_address": "Copy address", "global.copy_recovery_phrase": "Copy recovery phrase", "global.copy_token_contract": "Copy token contract", "global.copy_url": "Copy URL", "global.count_accounts": "{count, plural, one {# account} other {# accounts}}", "global.count_addresses": "{count, plural, one {# address} other {# addresses}}", "global.count_networks": "{count} networks", "global.create": "Create", "global.create_address": "Create address", "global.create_invoice": "Create invoice", "global.create_wallet": "Create wallet", "global.creating_address": "Creating address", "global.crop_image": "Crop image", "global.crypto": "Crypto", "global.current": "Current", "global.current_of_total_confirmations": "{current} of {total} Confirmations", "global.customize_home_screen": "Customize Home Screen", "global.customize_nonce": "Customize nonce", "global.customize_nonce_desc": "Turn this on to change the nonce (transaction number) and control your transaction order on confirmation > advance settings. This is an advanced feature, use cautiously.", "global.customize_transaction": "Customize transaction", "global.dapp_interaction": "dApp Interaction", "global.dark": "Dark", "global.date": "Date", "global.date_today": "Today", "global.date_yesterday": "Yesterday", "global.default": "<PERSON><PERSON><PERSON>", "global.delete": "Delete", "global.deny": "<PERSON><PERSON>", "global.derivation_path": "Derivation path", "global.description": "Description", "global.deselect_all": "Deselect all", "global.details": "Details", "global.dev_mode": "Dev mode", "global.device_info": "Device info", "global.device_management": "Device management", "global.dex": "DEX", "global.disable_button": "Turn off", "global.disabled": "Disabled", "global.discord": "Discord", "global.done": "Done", "global.download": "Download", "global.download_and_install": "Download and install", "global.earn": "<PERSON><PERSON><PERSON>", "global.edit": "Edit", "global.edit_avatar": "Edit avatar", "global.eg": "e.g.", "global.enable": "Enable", "global.enable_all": "Enable all", "global.enabled": "Enabled", "global.energy": "Energy", "global.energy_bandwidth": "Energy & Bandwidth", "global.energy_bandwidth_desc": "Transfers and smart contract calls prioritize using energy and bandwidth, which recover over time. If insufficient, TRX is consumed. You can stake TRX for bandwidth and buy or lease energy to reduce transaction fees.", "global.energy_bandwidth_learn": "How to get energy & bandwidth", "global.energy_bandwidth_num": "{num_1} Energy + {num_2} Bandwidth", "global.energy_bandwidth_transaction_desc": "The fee required for this transaction will be automatically deducted", "global.enter_hidden_wallet_pin": "Enter Hidden Wallet PIN", "global.enter_hidden_wallet_pin_on_device": "Enter Hidden Wallet PIN on Device", "global.enter_on_device": "Enter on device", "global.enter_passphrase": "Enter passphrase", "global.enter_passphrase_alert": "Passphrase is unrecoverable if lost", "global.enter_recovery_phrase": "Enter recovery phrase", "global.est_network_fee": "Est. network fee", "global.estimated_results": "Estimated results", "global.exit": "Exit", "global.expand": "Expand", "global.expand_view": "Expand view", "global.explore_now": "Explore now", "global.explorers": "Explorers", "global.export": "Export", "global.export_addresses": "Export addresses", "global.export_private_key": "Export private key", "global.face_id": "Face ID", "global.failed": "Failed", "global.faqs": "FAQs", "global.faqs_bluetooth_status": "Bluetooth status", "global.faqs_bootloader_mode": "Bootloader mode", "global.faqs_bridge_download": "Bridge download", "global.faqs_firmware_detection": "Connection Check", "global.faqs_forgot_pin": "Forgot PIN", "global.faqs_reset_wallet": "Reset wallet", "global.fcc_id": "FCC ID", "global.fdv": "FDV", "global.fetching_addresses": "Fetching addresses {current}/{total}", "global.filter": "Filter", "global.filter_by": "Filter by", "global.finish": "Finish", "global.firmware": "Firmware", "global.follow_the_system": "Follow the system", "global.for": "For", "global.forever": "Forever", "global.from": "From", "global.from_provider": "From {provider}", "global.gas_price": "Gas price", "global.general": "General", "global.generate_amount": "Generate amount", "global.generate_amount_address": "Address", "global.generate_amount_balance": "Balance", "global.generate_amount_confirm": "Confirm ({amount})", "global.generate_amount_error": "You can add up to {amount} more.", "global.generate_amount_information": "The maximum number of generated accounts is {max}.", "global.generate_amount_number": "No.", "global.generate_amount_select": "Select all", "global.generate_amount_select_path": "If your default address isn't visible, check Settings > Account derivation path.", "global.generate_max": "Max", "global.get_one": "Get one", "global.github": "GitHub", "global.go_settings": "Go settings", "global.go_to_settings": "Go to Settings", "global.google_drive": "Google Drive", "global.got_it": "Got it", "global.hardware_label": "Labeling", "global.hardware_label_desc": "Labels are applied on your device’s homescreen.", "global.hardware_label_input_error": "Invalid device labeling", "global.hardware_label_title": "Device labeling", "global.hardware_legacy_data_update_banner_description": "Connect your hardware wallet to update wallet data.", "global.hardware_legacy_data_update_banner_title": "Hardware wallet legacy data update", "global.hardware_legacy_data_update_dialog_button": "Update data", "global.hardware_legacy_data_update_dialog_description": "To complete this update, make sure your {walletName} is nearby and ready to connect.", "global.hardware_legacy_data_update_dialog_title": "Hardware wallet legacy data update", "global.hardware_legacy_data_update_modal_title": "Legacy data update", "global.hardware_name_desc": "Simultaneously change the device's name.", "global.hardware_name_input_error": "Invalid name", "global.hardware_name_input_max": "Exceeding the maximum word limit", "global.hardware_troubleshooting": "Hardware troubleshooting", "global.hardware_troubleshooting_contact": "Contact us", "global.hardware_troubleshooting_warranty_description": "Don't worry! Our support team is ready to assist you. Please contact customer support with your order number.", "global.hardware_troubleshooting_warranty_title": "Experiencing hardware issues?", "global.help_message": "Need help? <url>https://help.onekey.so/hc/requests/new<underline>Contact OneKey support</underline></url>.", "global.hex_data": "Message", "global.hex_data_default": "Hex data", "global.hex_data_default_faq": "What’s hex data?", "global.hex_data_error": "Input formatting error", "global.hex_data_faq": "What’s message?", "global.hex_data_faq_desc": "Additional data included for this transaction. Commonly used as part of contract interaction or as a message sent to the recipient.", "global.hex_data_input_default": "Enter message", "global.hex_data_input_desc_hex": "The current input is original data. UTF-8 is: {utf}.", "global.hex_data_input_desc_utf": "The current input is UTF-8. Original data is: {data}.", "global.hex_data_title": "Show message (hex data)", "global.hex_data_warning": "If you're not sure what Hex Data does, please do not enable this feature or include Hex Data in a transaction. Misuse may result in token approvals or asset transfers. Make sure you fully understand the risks before proceeding.", "global.hidden_wallet": "Hidden wallet", "global.hidden_wallet_desc": "A passphrase or hidden wallet PIN is required", "global.hide": "<PERSON>de", "global.history": "History", "global.homescreen": "Homescreen", "global.i_got_it": "I got it", "global.i_saved_the_phrase": "I've saved the phrase", "global.i_understand": "I understand", "global.icloud": "iCloud", "global.ignore": "Ignore", "global.import": "Import", "global.import_address": "Import address", "global.import_private_key": "Import private key", "global.import_progress": "Import progress", "global.import_recovery_phrase": "Import recovery phrase", "global.import_ton": "Importing TON wallet", "global.import_ton_desc": "This wallet uses the TON network for backup recovery phrases, supports only TON assets, and exists as a private key account, so sub-accounts can't be added.", "global.import_wallet": "Import wallet", "global.batch_import": "Batch Import", "global.batch_import_description": "Import multiple wallets at once", "global.batch_import_wallet": "Batch Import Wallet", "global.batch_import_wallet_description": "Import multiple wallets using private keys or recovery phrases", "global.batch_import_private_key": "Batch Import Private Keys", "global.batch_import_private_key_description": "Import multiple wallets using private keys", "global.batch_import_private_key_placeholder": "Enter private keys, one per line\n\nExample:\n0x1234567890abcdef...\n0xabcdef1234567890...", "global.batch_import_private_key_instruction": "Enter one private key per line. Each private key should be a 64-character hexadecimal string.", "global.batch_import_mnemonic": "Batch Import Recovery Phrases", "global.batch_import_mnemonic_description": "Import multiple wallets using recovery phrases", "global.batch_import_mnemonic_placeholder": "Enter recovery phrases, one per line\n\nExample:\nword1 word2 word3 ... word12\nword1 word2 word3 ... word24", "global.batch_import_mnemonic_instruction": "Enter one recovery phrase per line. Each phrase should contain 12 or 24 words separated by spaces.", "global.batch_import_instruction_title": "Instructions", "global.batch_import_preview": "Preview Import", "global.batch_import_result": "Import Results", "global.batch_import_invalid_warning": "Invalid Entries Found", "global.batch_import_invalid_warning_description": "Some entries are invalid and will be skipped during import.", "global.batch_import_partial_failure": "Partial Import Failure", "global.batch_import_partial_failure_description": "Some wallets failed to import. You can retry the failed imports.", "global.preview_summary": "Preview Summary", "global.wallet_list": "Wallet List", "global.import_summary": "Import Summary", "global.import_results": "Import Results", "global.import_completed": "Import Completed", "global.importing_progress": "Importing {current} of {total}...", "global.import_count_wallets": "Import {count} Wallets", "global.valid_count": "Valid: {count}", "global.invalid_count": "Invalid: {count}", "global.total_count": "Total: {count}", "global.success_count": "Success: {count}", "global.failed_count": "Failed: {count}", "global.retry_failed": "Retry Failed", "global.edit_wallet_name": "Edit Wallet Name", "global.wallet_name": "Wallet Name", "global.inactive": "Inactive", "global.info": "Info", "global.inputs": "Inputs", "global.install": "Install", "global.installing_firmware": "Installing firmware", "global.join": "Join", "global.language": "Language", "global.later": "Later", "global.layout": "Layout", "global.learn_more": "Learn more", "global.light": "Light", "global.link_copied": "Link copied", "global.links": "Links", "global.liquidity": "Liquidity", "global.logout": "Logout", "global.manage": "Manage", "global.manage_accounts": "Manage accounts", "global.mark_all_as_confirmation_desc": "This can’t be undone.", "global.mark_all_as_confirmation_title": "Mark all as read?", "global.market": "Market", "global.market_cap": "Market cap", "global.max": "Max", "global.max_est_network_fee": "Max est. network fee", "global.max_supply": "Max supply", "global.model": "Model", "global.month_date": "{month} {date}", "global.more": "More", "global.move_to_top": "Move to top", "global.multi_chain_wallet": "Multi-chain wallet", "global.multi_networks": "Multi-networks", "global.my_address": "My address", "global.my_onekey": "My OneKey", "global.name": "Name", "global.name_history": "Name history on this device", "global.names_of_wallets_and_accounts": "Names of wallets and accounts", "global.network": "Network", "global.network_error": "Network error", "global.network_error_help_text": "Check your connection and retry", "global.network_not_matched": "Network not matched", "global.network_type": "Network type", "global.networks": "Networks", "global.networks_information": "All networks may cause slow loading, Bitcoin and EVM networks will be added first. We recommend that you choose a single network.", "global.never": "Never", "global.next": "Next", "global.nft": "NFT", "global.no_data": "No data", "global.no_device_connected": "Ready to go", "global.no_device_connected_desc": "Manage all your devices in one place", "global.no_network_address": "No {network} address", "global.no_results": "No results", "global.no_transactions_yet": "No Transactions Yet", "global.no_transactions_yet_desc": "Your transactions will appear here", "global.no_wallet": "No wallet", "global.no_wallet_desc": "Add wallet to start managing your cryptocurrency safely and efficiently", "global.nonce": "<PERSON><PERSON>", "global.nonce_desc": "Current: {amount}", "global.nonce_error_higher": "Nonce is higher, it means that the transaction will be queued until the transactions in front of it are confirmed", "global.nonce_error_lower": "Nonce has been used and may cause this transaction to fail", "global.nonce_faq": "What’s <PERSON><PERSON>?", "global.nonce_faq_desc": "You have enabled the display of transaction serial numbers (nonce). You can edit the serial numbers to adjust the transaction order of your current account. This is an advanced feature, use cautiously.", "global.not_available": "Not available", "global.notifications": "Notifications", "global.number_accounts": "{number} Accounts", "global.number_items": "{number} Items", "global.off": "Off", "global.official_links": "Official links", "global.official_website": "Official website", "global.ok": "OK", "global.on": "On", "global.onekey_cloud": "OneKey Cloud", "global.onekey_keytag": "OneKey <PERSON>ag", "global.onekey_lite": "OneKey Lite", "global.onekey_prompt_dont_have_yet": "Don't have <PERSON><PERSON><PERSON> yet?", "global.onekey_wallet": "OneKey wallet", "global.onekey_wallet_extension": "OneKey wallet extension", "global.onekey_wallet_hardware_wallet": "OneKey hardware wallet", "global.or": "OR", "global.other_wallet": "Other wallet", "global.others": "Others", "global.outputs": "Outputs", "global.overview": "Overview", "global.overwritten": "Overwritten", "global.pair": "Pair", "global.pair_contract": "Pair contract", "global.passphrase": "Passphrase", "global.passphrase_desc": "Passphrase adds a custom phrase to your recovery phrase to create a hidden wallet. Each hidden wallet has its passphrase. Do not forget it, as it can't be retrieved & funds will be lost permanently.", "global.pause": "Pause", "global.pay": "Pay", "global.pct_complete": "{ptc}% complete", "global.pending": "Pending", "global.perp": "Perpetuals", "global.pin_to_top": "<PERSON>n", "global.pools": "Pools", "global.popular": "Popular", "global.portfolio": "Portfolio", "global.power_by": "Powered by", "global.preferences": "Preferences", "global.preparing": "Preparing...", "global.price": "Price", "global.prime_device_management": "Device management", "global.private_key": "Private key", "global.private_key_copy": "Copy Private Key?", "global.private_key_copy_check": "I’ve known my private key provides full access to my assets", "global.private_key_copy_information": "Never disclose this key. Anyone with your private keys can steal any assets held in your account.", "global.private_key_error": "This account does not have a {network} {path} address yet. Please create one to continue.", "global.process": "Process", "global.processing": "Processing", "global.profit": "Profit", "global.protocol": "Protocol", "global.public_key": "Public key", "global.public_key_export": "Export public key", "global.qr_code": "QR code", "global.quit": "Quit", "global.receive": "Receive", "global.receive_address_confirmation": "Address confirmation", "global.receive_address_confirmation_button": "I’ll take the risk", "global.receive_address_confirmation_desc": "Unverified actions may result in assets being sent to the wrong address, especially if the OneKey App is not reconnected after local data tampering or reset.", "global.recent_searched": "Recent", "global.recents": "Recents", "global.recipient": "Recipient", "global.recommend": "Recommend", "global.recovery_phrase": "Recovery phrase", "global.refresh": "Refresh", "global.remove": "Remove", "global.remove_account_desc": "You can restore this account later in this wallet by using \"Manage accounts\".", "global.remove_account_name": "Remove {account}", "global.rename": "<PERSON><PERSON>", "global.request_limit": "Too many requests, please try again later.", "global.reset": "Reset", "global.resources": "Resources", "global.restore": "Rest<PERSON>", "global.resume": "Resume", "global.retry": "Retry", "global.review": "Review", "global.review_again": "Review again", "global.review_order": "Review order", "global.revoke": "Revoke", "global.revoke_approve": "Revoke {symbol} allowance", "global.risk": "Risk", "global.sats": "sats", "global.saved_the_phrases": "I've saved the phrases", "global.saved_the_private_key": "I've saved the private key", "global.scan_to_connect": "Scan to connect", "global.search": "Search", "global.search_account_selector": "Search account name", "global.search_address": "Search address", "global.search_asset": "Search asset", "global.search_no_results_desc": "Try to change the search keyword", "global.search_no_results_title": "No results", "global.search_tokens": "Search token", "global.secure_install": "Secure install", "global.security": "Security", "global.select_all": "Select all", "global.select_crypto": "Select crypto", "global.select_network": "Select network", "global.select_wallet": "Select wallet", "global.select_wallet_type_to_add": "Select Wallet Type to Add", "global.sell": "<PERSON>ll", "global.send": "Send", "global.sent_successfully": "<PERSON><PERSON> successfully", "global.serial_number": "Serial number", "global.serial_number_start": "Serial number of addresses to start", "global.serial_number_start_desc": "Adding address from {from} to {to}.", "global.server_side": "Server-side", "global.set_as_wallpaper_pair": "Set as Wallpaper Pair", "global.set_passcode": "Set passcode", "global.set_password": "Set password", "global.settings": "Settings", "global.settings_up": "Setting up", "global.show_less": "Show less", "global.show_more": "Show more", "global.show_recovery_phrase": "Show recovery phrase", "global.sign": "Sign", "global.sign_in_register": "Sign in / Register", "global.single_chain_account": "Single-chain account", "global.skip": "<PERSON><PERSON>", "global.skip_for_now": "Skip for now", "global.software_wallet": "Software wallet", "global.source_code": "Source code", "global.sped_up": "Sped up", "global.speed_up": "Speed up", "global.standard_wallet": "Standard wallet", "global.standard_wallet_desc": "No passphrase", "global.start_connection": "Start connection", "global.start_migration": "Start migration", "global.status": "Status", "global.step_str": "Step {step}", "global.storage_used": "Storage used", "global.success": "Success", "global.support": "Support", "global.swap": "<PERSON><PERSON><PERSON>", "global.switch": "Switch", "global.switch_address": "Switch address", "global.switch_supported_accounts_wallets": "Please switch to supported accounts or wallets.", "global.sync_error": "Sync error", "global.synced": "Synced", "global.syncing": "Syncing...", "global.system_fee": "System fee", "global.test": "Test", "global.testnet": "Testnet", "global.time": "Time", "global.to": "To", "global.total_in": "Total in", "global.total_out_include_fee": "Total out (Include fee)", "global.total_supply": "Total supply", "global.touch_id": "Touch ID", "global.touch_id_set_error": "Set biology auth fail", "global.track": "Watch", "global.track_any_address": "Track any address", "global.track_any_address_description": "Monitor any wallet address without connecting your own wallet", "global.trade": "Trade", "global.transaction_id": "Transaction hash", "global.transfer": "Transfer", "global.transfer_accounts_count": "{amount} accounts", "global.transfer_accounts_loading": "{amount} account(s) added", "global.try_again": "Try again", "global.unhide": "Unhide", "global.universal_search_placeholder": "Search accounts, tokens, dApps...", "global.universal_search_tabs_all": "All", "global.universal_search_tabs_dapps": "dApps", "global.universal_search_tabs_my_assets": "My assets", "global.universal_search_tabs_tokens": "Tokens", "global.universal_search_tabs_wallets": "Accounts", "global.unknown": "Unknown", "global.unknown_error": "Unknown error", "global.unknown_error_retry_message": "An unexpected error occurred. Please try again.", "global.unlock": "Unlock", "global.unofficial": "Unofficial", "global.unpin_from_top": "Unpin", "global.unverified": "Unverified", "global.update_failed": "Update Failed", "global.updating": "Updating...", "global.updating_type": "Updating {type}...", "global.upgrade": "Upgrade", "global.url": "URL", "global.value": "Value", "global.verified": "Verified", "global.verify": "Verify", "global.verify_on_device": "Verify on device", "global.version_is_available": "{version} is available", "global.version_mismatch": "Version mismatch", "global.view": "View", "global.view_in_blockchain_explorer": "View in explorer", "global.view_less": "View less", "global.view_more": "View more", "global.view_tutorial": "View tutorial", "global.visit_website": "Visit website", "global.wallet": "Wallet", "global.wallet_avatar": "Wallet avatar", "global.wallets": "Wallets", "global.wallpaper": "Wallpaper", "global.wallpaper_classic": "Classic", "global.wallpaper_cobranding": "Co-branding", "global.wallpaper_collection": "collection", "global.wallpaper_custom": "Custom", "global.wallpaper_custom_max_limit": "Reached limit of {0} wallpapers. Please delete one to add.", "global.warning": "Warning", "global.watch_only": "Watch only", "global.watch_only_address": "Watch-only address", "global.watch_only_wallet": "Watch-only wallet", "global.watched": "Watch-Only", "global.watchlist": "Watchlist", "global.website": "Website", "global.white_paper": "White paper", "global.windows_hello": "Windows Hello", "global.windows_hello_set_error": "Set Windows Hello fail", "global.withdraw": "Withdraw", "global.x": "X", "hardware.backup_completed": "Backup completed!", "hardware.bluetooth_need_turned_on_error": "Bluetooth is off", "hardware.bluetooth_not_paired_error": "Bluetooth not paired", "hardware.bluetooth_pairing_failed": "Bluetooth pairing failed", "hardware.bluetooth_requires_permission_error": "Bluetooth permission required", "hardware.bridge_network_error": "Unable to connect to Bridge, please check your network connection and try again", "hardware.bridge_timeout": "Bridge connection timeout, please check your network connection and try again", "hardware.bridge_timeout_for_desktop": "Bridge connection timeout, please check your network connection and try again", "hardware.communication_error": "Hardware communication error. Please restart the app and your hardware device, then try again.", "hardware.confirm_onekey_lite_pin": "Confirm OneKey Lite PIN", "hardware.confirm_onekey_lite_pin_desc": "Please re-enter the OneKey Lite PIN you just set.", "hardware.connect_failed": "Connect failed", "hardware.connect_timeout_error": "Connection timeout", "hardware.connect_unknown_device_error": "Unknown device, the current version App does not support connection.", "hardware.data_erased_use_as_new": "The data on this OneKey Lite has been completely erased, and you can use it as a new OneKey Lite.", "hardware.default_error": "Hardware communication failed, please try again", "hardware.device_ble_already_connected": "<PERSON><PERSON> already connected elsewhere, please disconnect and try again", "hardware.device_ble_location_disabled": "Location services unavailable, please enable and try again", "hardware.device_ble_scan_error": "Bluetooth scan failed, please try again", "hardware.device_ble_scan_throttle": "Bluetooth scan too frequent, please try again later", "hardware.device_connected_keep_card_in_place": "The device is connected, please keep the card in place and do not move it.", "hardware.device_contains_backup": "This device contains backup", "hardware.device_contains_backup_desc": "If you continue, your previous backup will be fully overwritten and will be lost forever.", "hardware.device_information_is_inconsistent_it_may_be_caused_by_device_reset": "Device connection status has been updated. Please select \"Add Wallet\" > \"Connect Hardware Wallet\" to reconfigure. Using your original recovery phrase will restore your current wallet, while using a new recovery phrase will create a new wallet.", "hardware.device_need_restart": "Hardware communication failed, please restart the device and try again", "hardware.device_not_find_error": "Device not found. Please try reconnecting the device (unplug and plug the USB or turn Bluetooth off and on), then try again.", "hardware.device_passphrase_state_error": "Passphrase does not match the current wallet, please try again", "hardware.device_pin_state_error": "The entered PIN does not correspond to the current wallet. Please try again.", "hardware.ensure_device_close_to_nfc": "Make sure the device is close to the phone's NFC module, then try again.", "hardware.enter_boot_failure": "Failed to enter update mode", "hardware.enter_onekey_lite_pin": "Enter OneKey Lite PIN", "hardware.enter_onekey_lite_pin_desc": "OneKey Lite PIN is a 6-digit number and cannot be retrieved if forgotten, as we do not store any user information.", "hardware.enter_passphrase_on_device": "Enter Passphrase on <PERSON><PERSON>", "hardware.failed_to_search_devices": "Search failed", "hardware.file_already_exists": "File already exists", "hardware.firmware_download_error": "Firmware download failed", "hardware.hardware_device_not_find_error": "Devi<PERSON> not found", "hardware.hardware_params_bytes_overload": "The data length exceeds the hardware's processing capacity", "hardware.incomplete_file": "Incomplete file", "hardware.init_iframe_load_error": "Hardware SDK initialization failed. Please check your network or switch the proxy and try again.", "hardware.invalid_pin_error": "Invalid PIN", "hardware.keep_lite_placed_until_found": "Please keep <PERSON><PERSON> placed with the phone until the device is found.", "hardware.manually_enter_boot": "Manual boot entry required", "hardware.need_force_upgrade_firmware": "Firmware needs to be upgraded", "hardware.no_backup_inside": "No backup inside", "hardware.no_backup_inside_desc": "No backup in this One<PERSON>ey <PERSON>. Replace with another OneKey Lite and retry.", "hardware.no_connection_desc": "Please try again or check your network", "hardware.no_pin_change_needed": "No need to change the PIN code on this new OneKey Lite card.", "hardware.not_opened_passphrase": "Please enable Passphrase on the device to use the hidden wallet", "hardware.not_same": "Mismatch, please connect the correct device", "hardware.not_support": "This method has security risks and is not supported by the hardware", "hardware.not_support_passphrase_need_upgrade": "Using Passphrase requires upgrading the firmware to version {version} or higher.", "hardware.onekey_lite_pin_changed": "OneKey Lite PIN changed!", "hardware.onekey_lite_pin_changed_desc": "This OneKey Lite's PIN has been changed.\nRemember this PIN as it cannot be recovered if lost, as we do not store any user information.", "hardware.onekey_lite_pin_error": "OneKey Lite PIN error", "hardware.onekey_lite_pin_error_desc": "After <red>{number}</red> more wrong tries, the data on this OneKey Lite will be erased.", "hardware.onekey_lite_reset": "OneKey Lite has been reset", "hardware.only_one_device_can_be_connected_desc": "Please connect only one device for the upgrade.", "hardware.open_blind_sign_error": "Unknown signature format, please go to Security/Blind Signing to enable SOL advanced signing", "hardware.opened_passphrase": "Please disable Passphrase on the device to use the standard wallet", "hardware.params_bytes_overload": "Data size exceeds hardware limit. Please update the firmware and try again or contact customer support", "hardware.passphrase_enter_too_long": "passphrase supports a maximum of {0} characters", "hardware.pin_change_failed": "PIN change failed", "hardware.pin_incorrect_data_erased": "The PIN code has been entered incorrectly over 10 times. To prevent unauthorized access to the backup data, the data on this device has been erased.", "hardware.pins_do_not_match": "The entered PINs do not match. Please reconfirm.", "hardware.place_onekey_lite_close_to_phone": "Place OneKey Lite close to the phone", "hardware.place_onekey_lite_close_to_phone_desc": "Place the Lite and phone as shown in the figure below, then click \"connect.”", "hardware.polling_connect_timeout_error": "Connection timeout. Please check the OneKey Bridge status and restart the hardware", "hardware.recover_wallet_with_card_and_pin": "You can recover your wallet using this card and PIN at all times.\nRemember this PIN as it cannot be recovered if lost, as we do not store any user information.", "hardware.recovery_interrupted": "Recovery interrupted", "hardware.reset_failed": "Reset failed", "hardware.reset_failed_desc": "This is a brand new OneKey Lite that does not require resetting.", "hardware.reset_onekey_lite": "Reset OneKey Lite", "hardware.reset_onekey_lite_desc": "Please ensure that you have backed up the recovery phrase before entering \"RESET\" to confirm, as it will be erased from this OneKey Lite device.", "hardware.searching_for_device": "Searching for device...", "hardware.set_onekey_lite_pin": "Set OneKey Lite PIN", "hardware.set_onekey_lite_pin_desc": "Set a 6-digit PIN for your OneKey Lite.", "hardware.software_cannot_be_upgrade": "The client cannot update the hardware. You need to go to <url>https://firmware.onekey.so](https://firmware.onekey.so/)</url> to update", "hardware.status_connected": "Connected", "hardware.status_update_available": "Update available", "hardware.str_not_supported_by_hardware_wallets": "Waiting for firmware update, hardware wallets are not supported at this time.", "hardware.transaction_signing_error_not_in_signing_mode": "Transaction signing error: Not in signing mode", "hardware.transferring_data": "Transferring data...", "hardware.two_onekey_lite_not_same": "The two OneKey Lite used for connection are not the same.", "hardware.unknown_message_error": "Current firmware not supported, please try upgrading", "hardware.unsupported_passphrase_characters": "Contains unsupported characters", "hardware.user_cancel_error": "Action canceled", "hardware.verify_success": "Official firmware", "hardware.version_need_upgrade_error": "Please upgrade the firmware to version {version} or higher to use this feature", "hardware.version_too_low_error": "Current firmware version is too low, please update the firmware", "hardware.wallet_connection_is_not_currently_supported_but_we_are_working_on_it_stay_tuned": "Hardware wallet connection is not currently supported, but we are working on it. Stay tuned!", "hardware.wallet_connection_is_only_available_on_the_official_app": "Hardware wallet connection is only available on the official app", "hardware.wallet_connection_is_only_available_on_the_third_party_apps": "Hardware wallet connection is only available on third-party apps", "hardware_defective_firmware_error": "We’ve identified that this device may belong to an early batch. To protect your assets, its use in the OneKey client has been suspended. Please contact customer support for a replacement.", "hardware_defective_firmware_error_title": "For your security, this device has been temporarily disabled", "hidden_assets": "Hidden assets", "hidden_wallet_accessibility_title": "<strong>Keep accessible</strong>. Hidden wallets stay after you close the app", "history.notification_receiver_label": "Notification received by", "history.switch_account_dialog_title": "Switch primary account to {account}?", "hw_banner_description": "Secure your crypto with the most powerful hardware wallet", "id.delete_double_check": "Are you sure you want to delete OneKey ID ?", "id.delete_onekey_id": "Delete OneKey ID", "id.delete_onekey_id_desc": "By deleting OneKey ID, you will lose access to the following benefits. This action cannot be undone.", "id.delete_onekey_id_detail_markdown": "- **OneKey Prime Membership:** You will permanently lose your Prime member status and all associated exclusive benefits.\n- **OneKey Cloud data:** All of your data synced to the cloud will be permanently deleted.\n- **Referral Program:** Your referral code will be deactivated, and your entire history of referrals and future rewards will be deleted.", "id.desc": "Access all OneKey services and earn referral rewards.", "id.login_expired_description": "Your OneKey ID session has expired. Please log in again.", "id.login_failed": "<PERSON><PERSON> failed, please try again later.", "id.login_privy_limit": "Too many requests, please try again later.", "id.login_success": "Login successful", "id.onekey_id_deleted_desc": "OneKey ID and all associated data have been permanently removed. Thank you for using our service.", "id.onekey_id_deleted_title": "Your OneKey ID has been deleted", "id.prime": "Unlock advanced features", "id.prime_macos_app_store": "Unlock Prime features. (iOS version only)", "id.prime_soon": "Soon", "id.refer_a_friend": "Refer a Friend", "id.refer_a_friend_desc": "Invite friends to earn rewards", "insufficient_fee_append_desc": "based on max est. fee: {amount} {symbol}", "interact_with_contract": "Interact with (To)", "kaspa_official": "Kaspa Official", "lightning_invoice": "Lightning Invoice", "limit.cancel_order_content": "Are you sure you want to cancel order {orderID}", "limit.cancel_order_off_chain_tip": "This is an off-chain cancellation. It requires a signature and are free.", "limit.cancel_order_title": "Cancel order", "limit.enter_price": "Enter a price", "limit.est_fee": "Est. fee", "limit.est_to": "Est. to", "limit.fill_at": "<PERSON><PERSON><PERSON> at", "limit.fill_at_popover": "The order need a bit more than your limit price to execute so that cover the fee.", "limit.history_fill_sold": "{num1} {token1} sold for total for {num2} {token2}", "limit.network_cost_dialog_content": "The network fee for this order is relatively high compared to the total order amount. This order is unlikely to be filled or the execution price may differ a lot from your limit price.", "limit.order_card_network": "Network:", "limit.order_info_network_cost": "Network cost", "limit.place_order": "Place limit order", "limit.place_order_step_2": "2. Place limit order", "limit.price_trigger": "Trigger distance", "limit.price_trigger_des_down": "This price is {num} lower than current market price. You could be at a loss! Click on \"Market price\" to set your limit price to the current market price.", "limit.price_trigger_des_up": "Your order will execute when the market price is {num} better than the current market price.", "limit.relative_to_market": "Relative to market price:", "limit.toast_order_cancelled": "Order cancelled", "limit.toast_order_content": "Sell {num1} {token1} for {num2} {token2}", "limit.toast_order_filled": "Order filled", "limit.toast_order_submitted": "Order submitted", "limit_order.expired": "Expired", "limit_page.limit_steps_2": "Place order", "list_section_unavailable_networks_text": "Unavailable networks for selected account", "litecoin_legacy_desc": "Starts with \"L\". BIP44, P2PK<PERSON>, Base58", "litecoin_native_segwit_desc": "Starts with \"ltc1\". BIP84, P2WP<PERSON><PERSON>, Bech32", "litecoin_nested_segwit_desc": "Starts with \"M\". BIP49, P2SH-P2WPKH, Base58", "ln.authorize_access": "Authorize access", "ln.authorize_access_desc": "Connecting your hardware wallet to access the Lightning account", "ln.authorize_access_network_error": "Authentication failed, check your network connection and try again", "ln.payment_received_label": "Payment received", "login.forgot_passcode": "Forgot passcode?", "login.forgot_password": "Forgot password?", "login.welcome_message": "Welcome back", "low_value_assets": "Low-value assets", "low_value_assets_desc": "Assets valued below 0.1% of your total holdings and less than $1,000 fall into this category.", "low_value_assets_desc_out_of_range": "Assets ranked below the top 50 in total value when owning multiple assets.", "manage_token.account_no_found": "Insufficient {token} to cover network fee. Please add more {token} and retry.", "manage_token.added_token": "Added token", "manage_token.custom_token_add": "2. Add", "manage_token.custom_token_add_btn": "Add", "manage_token.custom_token_address_required": "Contract address required.", "manage_token.custom_token_button": "Add custom token", "manage_token.custom_token_chain_input": "Chain", "manage_token.custom_token_contract_address": "Contract address", "manage_token.custom_token_create_address": "1. Create address", "manage_token.custom_token_decimal": "Decimal", "manage_token.custom_token_symbol": "Symbol", "manage_token.custom_token_title": "Custom token", "manage_token.empty_msg": "Cannot find the token?", "manage_token.native_token_cannot_removed": "Native token cannot be removed", "manage_token.native_token_cant_remove": "Native token can't be removed", "manage_token.popular_token": "Common token", "manage_token.title": "Manage token", "manage_token.token_required": "Symbol is required.", "manual_backup": "Manual backup", "market.1d": "1D", "market.1h": "1H", "market.1m": "1M", "market.1w": "1W", "market.1y": "1Y", "market.24h": "24H", "market.24h_price_range": "24H price range", "market.24h_txns": "24H txns", "market.24h_vol_usd": "24H VOL(USD)", "market.30d": "30D", "market.7d": "7D", "market.add_number_tokens": "Add {number} tokens", "market.add_to_watchlist": "Add to watchlist", "market.added_to_watchlist": "Added to market watchlist", "market.all_time_high": "All-time high", "market.all_time_low": "All-time low", "market.ath_desc": "{token}’s all-time high was on {time}, at {price}, and the current price is down by {percent} from that high.", "market.atl_desc": "{token}’s all-time low was on {time}, at {price}, and the current price is up by {percent} from that low.", "market.cex": "CEX", "market.chart": "Chart", "market.days_since_launch": "Days since launch", "market.empty_watchlist_desc": "Add your favorite tokens to watchlist", "market.empty_watchlist_title": "Your watchlist is empty", "market.high": "High", "market.last_price": "Last price", "market.last_seven_days": "Last 7 days", "market.last_updated": "Last updated", "market.lite_chart": "Lite chart", "market.low": "Low", "market.minus_2_percent_depth": "-2% depth", "market.most_24h_volume": "Most 24h volume", "market.most_market_cap": "Most market cap", "market.move_to_top": "Move to top", "market.number_of_days": "{number} days", "market.one_hour_percentage": "1h%", "market.pair": "Pair", "market.pair_link": "Pair link:", "market.plus_2_percent_depth": "+2% depth", "market.pool_details": "Pool details", "market.price_change_down": "Price change down", "market.price_change_up": "Price change up", "market.remove_from_watchlist": "Remove from watchlist", "market.seven_day_percentage": "7d%", "market.sort_by": "Sort by", "market.spread": "Spread", "market.trending": "Trending", "market.trust_score": "Trust core", "market.twenty_four_hour_percentage": "24h%", "market.twenty_four_hour_volume": "24h volume", "market.volume_percentage": "Volume %", "max_base_fee_lower_then_base_fee_alert_message": "The max base fee is lower than the base fee, which may cause a delay in the transaction.", "menu.about_onekey_wallet": "About OneKey Wallet", "menu.actual_size": "Actual Size", "menu.bring_all_to_front": "Bring All to Front", "menu.check_for_updates": "Check for Updates...", "menu.cut": "Cut", "menu.help": "Help", "menu.hide_onekey_wallet": "Hide OneKey Wallet", "menu.lock_now": "Lock Now", "menu.minimize": "Minimize", "menu.official_website": "Official Website", "menu.paste": "Paste", "menu.preferences": "Preferences..", "menu.quit_onekey_wallet": "Quit OneKey Wallet", "menu.redo": "Redo", "menu.select_all": "Select All", "menu.show_all": "Show All", "menu.toggle_full_screen": "Toggle Full Screen", "menu.undo": "Undo", "menu.view": "View", "menu.visit_help_center": "Visit Help Center", "menu.window": "Window", "menu.zoom": "Zoom", "menu.zoom_in": "Zoom In", "menu.zoom_out": "Zoom Out", "menu__copy_image": "Copy image", "menu__save_image_as": "Save image as…", "message_signing.address_desc": "Sign a message to prove ownership of your Ethereum address, commonly required by exchanges like OSL", "message_signing.address_hex_format": "Hex format", "message_signing.address_invalid_text": "Not a valid Bitcoin or Ethereum address", "message_signing.address_placeholder": "Enter a message to prove ownership of this address...", "message_signing.main_title": "Sign & verify message", "message_signing.sign_action": "Sign message", "message_signing.signature_desc": "Generated after signing", "message_signing.signature_invalid_text": "Invalid signature", "message_signing.signature_label": "Signature", "message_signing.verification_success": "Message verification successful", "message_signing.verify_action": "Verify message", "mev_protection_desc": "Transactions will be broadcast to the designated Anti-MEV node to reduce MEV attack risks.", "mev_protection_label": "MEV protection", "mev_protection_note": "This feature is not supported if you use a custom RPC or external wallet.", "msg__str_is_required_for_network_fees_top_up_str_to_make_tx": "Insufficient {symbol} for network fees. Add at least {amount} {symbol}", "msg__transaction_with_the_same_nonce_already_exist_please_pay_a_higher_network_fee_otherwise_the_transaction_may_fail": "Transactions with the same nonce already exist, please pay a higher network fee, otherwise the transaction may fail.", "native_token_tooltip": "This is the cryptocurrency used to pay for network fees", "network.all_networks_selection_tip": "When you select \"All Networks\", you can customize which networks to include. To keep loading fast, only popular networks are enabled by default—but you can easily enable additional networks here.", "network.enabled_but_no_address_notice": "You have {count} enabled networks, but some don’t have addresses yet", "network.recent_used_network": "Recent", "network__network": "Network", "network_also_enabled": "This network has also been enabled", "network_enable_count": "Enable {count} networks", "network_enable_more": "Enable more networks", "network_enabled_count": "{count} enabled", "network_fee_suggested_by_dapp_description": "Switched to dApp-suggested gas to save on fees", "network_fee_suggested_by_dapp_label": "Suggested by dApp", "network_invisible_in_all_network_tooltip_title": "Hidden in 'All networks' view", "network_none_selected": "No networks selected", "network_not_enabled": "Not enabled", "network_selection_prompt": "Select the networks you want to enable", "network_selector.unavailable_networks": "Unavailable networks for selected account", "network_show_enabled_only": "Only show enabled networks", "network_visible_in_all_network_tooltip_title": "Shown in 'All networks' view", "nft.already_collected": "This NFT has already been collected.", "nft.attributes": "Attributes", "nft.collect_failed": "NFT collect failed, please try again.", "nft.collect_to_touch": "Collect to {device}", "nft.collection": "Collection", "nft.contract_address": "Contract Address", "nft.floor_price": "Floor price", "nft.highest_floor": "Highest floor", "nft.last_sale": "Last sale", "nft.mint_address": "Mint Address", "nft.more": "More", "nft.nft_standard": "NFT Standard", "nft.no_attributes_found": "We haven't found any attributes for this NFT.", "nft.no_nfts": "No NFTs", "nft.no_nfts_found": "No NFTs found at this address", "nft.no_results": "No Results", "nft.owner_program": "Owner Program", "nft.recently_received": "Recently received", "nft.search_result_count": " {number} Results", "nft.sort": "Sort", "nft.token_address": "Token Address", "nft.token_id": "Token ID", "no_account": "No account", "no_external_wallet_message": "No external wallets are connected. Link a third-party wallet to view here.", "no_private_key_account_message": "No private key accounts. Add a new account to manage your assets.", "no_standard_wallet_desc": "No standard wallet yet", "no_transaction_desc": "Your transactions will appear here", "no_transaction_title": "No transactions yet", "no_watched_account_message": "No watch-only accounts. Import an address to start monitoring.", "notifications.account_activity_manage_count_alert_prime_desc": "Subscribe to Prime for more", "notifications.account_activity_manage_count_alert_title": "{totalEnabledAccountsCount}/{maxAccountCount} accounts enabled", "notifications.account_reached_limit_alert_desc": "New accounts may not get notifications.", "notifications.account_reached_limit_alert_title": "{count} accounts limit reached", "notifications.account_reached_limit_dialog_desc": "You've reached the maximum of {maxAccountCount} accounts.", "notifications.account_reached_limit_dialog_prime_desc": "You've reached the maximum of {maxAccountCount} accounts. Subscribe to Prime to enable more accounts.", "notifications.account_reached_limit_dialog_title": "Account limit reached", "notifications.empty_desc": "You have not received any notifications yet", "notifications.empty_title": "No notifications", "notifications.intro_desc": "Allow push notifications to receive alerts for account activity directly on your device", "notifications.intro_mac_desc": "Click 'Enable,' then turn on the switch", "notifications.intro_title": "Enable push notifications", "notifications.mac_permission_title": "Allow notifications", "notifications.notifications_account_activity_desc": "Get notified when receiving, sending, trading, and more.", "notifications.notifications_account_activity_label": "Account activity", "notifications.notifications_account_manage_desc": "Choose the account for notifications.", "notifications.notifications_account_manage_label": "Manage", "notifications.notifications_price_alert_desc": "Get notified when the watched cryptos increase or drop more than 10% in 24 hours.", "notifications.notifications_price_alert_label": "Price alert", "notifications.notifications_switch_label": "Allow notifications", "notifications.permission_instruction": "Click 'Enable,' then allow notifications for <PERSON><PERSON>ey.", "notifications.settings_helper_desc": "Besides receiving notifications directly in the app, OneKey also supports system push notifications.", "notifications.settings_helper_title": "Push notifications", "notifications.test_action_desc": "Click \"Test\" to preview. For help, visit the <tag>help center</tag>.", "notifications.test_message_desc": "You’ll receive real-time account updates, security alerts, and more important info.", "notifications.test_message_title": "Push notifications are ready!", "notifications.windows_notifications_permission_desc": "Get notifications from apps and other senders", "onboarding.activate_device": "Activate your device", "onboarding.activate_device_all_set": "All set!", "onboarding.activate_device_by_restore": "Restore wallet", "onboarding.activate_device_by_restore_help_text": "Restore your wallet using an existing recovery phrase.", "onboarding.activate_device_by_restore_warning": "Package security check", "onboarding.activate_device_by_restore_warning_help_text": "Your package should not contain any pre-set PINs or Recovery Phrases. If such items are found, stop using the device and immediately contact OneKey Support for assistance.", "onboarding.activate_device_by_set_up_new_wallet": "Set up new wallet", "onboarding.activate_device_by_set_up_new_wallet_help_text": "Configure your device to create a new wallet.", "onboarding.activate_device_choosing_language_message": "After choosing a language on your device and reviewing the basic guide:", "onboarding.activate_device_help_text": "Set up your hardware wallet to get started.", "onboarding.backup_recovery_phrase_help_text": "Write down each phrase in order and store them in a secure location", "onboarding.backup_recovery_phrase_title": "Backup recovery phrases", "onboarding.before_reveal_message": "Before you proceed", "onboarding.bluetooth_connect_help_text": "Looking for devices", "onboarding.bluetooth_permission_needed": "Bluetooth permission needed", "onboarding.bluetooth_permission_needed_help_text": "To connect via Bluetooth, please enable access in Settings.", "onboarding.bluetooth_prepare_to_connect": "Keep the device nearby", "onboarding.bluetooth_prepare_to_connect_help_text": "Ensure the device is powered on and within range, then press \"Start connection\" below to start the connection", "onboarding.bullet_forgot_passcode_use_recovery": "Use it to restore your wallet if you forget your passcode", "onboarding.bullet_forgot_password_use_recovery": "If you forget your password, you can use the recovery phrase to get back into your wallet", "onboarding.bullet_never_share_recovery_phrase": "Never share it or enter it anywhere", "onboarding.bullet_onekey_support_no_recovery_phrase": "OneKey Support will never ask for it", "onboarding.bullet_recovery_phrase_full_access": "Your recovery phrase gives full access to your wallet and funds", "onboarding.choose_import_method": "Choose import method", "onboarding.connect_external_wallet": "Connect external wallet", "onboarding.connect_your_device": "Connect your device", "onboarding.create_new_wallet": "Create new wallet", "onboarding.create_or_import_wallet": "Create or import wallet", "onboarding.create_qr_wallet_scan_qr_code_desc": "Back to here, click the button below to scan the QR code", "onboarding.create_qr_wallet_show_qr_code_desc": "Go to \"Connect App Wallet\" > \"QR Code\" > \"OneKey Wallet\" to view the QR code", "onboarding.create_qr_wallet_title": "Create QR-based wallet", "onboarding.create_qr_wallet_unlock_device_desc": "Unlock your OneKey Pro", "onboarding.device_mini_set_up_import": "Select \"Restore Wallet\"", "onboarding.device_set_up_backup": "Write down all recovery phrase", "onboarding.device_set_up_backup_desc": "Securely record your recovery phrase and complete the check. It's crucial for accessing your wallet if you forget your PIN or lose your device.", "onboarding.device_set_up_create_new_wallet": "Select \"Create New Wallet\"", "onboarding.device_set_up_create_new_wallet_desc": "Initiate your wallet setup by selecting \"Create New Wallet\". You will be guided through creating a unique recovery phrase and setting a secure PIN.", "onboarding.device_set_up_enter_recovery_phrase": "Enter recovery phrase", "onboarding.device_set_up_enter_recovery_phrase_desc": "Carefully enter your recovery phrase word by word. This unique phrase is essential for the security and recovery of your wallet.", "onboarding.device_set_up_import": "Select \"Import Wallet\"", "onboarding.device_set_up_import_desc": "Follow the prompts to secure your wallet with a PIN and enter your recovery phrase to complete the process.", "onboarding.device_set_up_pin": "Set PIN", "onboarding.device_set_up_pin_desc": "Create a strong PIN to protect your wallet just like you would with a bank card. Avoid easy sequences or repeated numbers.", "onboarding.enable_bluetooth": "Enable Bluetooth", "onboarding.enable_bluetooth_help_text": "Bluetooth needs to be turned on to proceed.", "onboarding.finalize_creating_wallet": "Creating your wallet", "onboarding.finalize_encrypting_data": "Encrypting your data", "onboarding.finalize_generating_accounts": "Generating your accounts", "onboarding.finalize_ready": "Your wallet is now ready", "onboarding.finalize_wallet_setup": "Finalize wallet setup", "onboarding.import_recovery_phrase_warning": "Security alert", "onboarding.import_recovery_phrase_warning_help_text": "For your asset safety, avoid importing the recovery phrase of your hardware wallet and use \"Connect Hardware Wallet\" for optimal security.", "onboarding.install_onekey_bridge": "Install OneKey Bridge", "onboarding.install_onekey_bridge_help_text": "OneKey Bridge facilitates seamless communication between OneKey and your browser for a better experience.\n\nIf you encounter issues during the installation of OneKey Bridge, please refer to the <url>https://help.onekey.so/articles/********<underline>online tutorial</underline></url> for assistance.", "onboarding.migrate_from_v4": "Migrate from OneKey v4", "onboarding.onekey_hw": "OneKey Hardware Wallet", "onboarding.onekey_hw_intro_desc": "OneKey Hardware Wallet, a secure and user-friendly solution for crypto management. It supports multiple cryptocurrencies and ensures robust encryption for safe transactions.", "onboarding.onekey_hw_intro_title": "Your secure crypto solution", "onboarding.save_phrase_securely_instruction": "Read the following, then save the phrase securely", "onboarding.transfer_desc": "Transfer wallets between devices", "onboarding.usb_connect_help_text": "Connect your device via USB", "onboarding.verify_recovery_phrase_title": "Verify your recovery phrase", "onboarding.welcome_description": "Simple, secure crypto management", "onboarding.welcome_message": "Welcome to OneKey", "onboarding_import_wallet_desc": "Transfer, restore, import or watch-only", "onekey_extended": "OneKey Extended", "open_as_popup": "Open as popup", "open_as_sidebar": "Open as side panel", "open_in_mobile_app": "Open in Mobile App", "open_ordinals_transfer_tutorial_url_message": "How to transfer Ordinals assets?", "p2pkh_desc": "Starts with \"1\". P2PKH (m/44'/0'/0')", "p2sh_p2wpkh_desc": "Starts with \"bc1q\". P2SH-P2WPKH (m/84'/0'/0')", "p2tr_desc": "Starts with \"bc1p\". P2TR (m/86'/0'/0')", "p2wpkh_desc": "Starts with \"3\". P2WPKH (m/49'/0'/0')", "passphrase_allowed_characters_desc": "Letters, numbers & symbols (ASCII 32-126)", "passphrase_allowed_characters_title": "Allowed characters", "passphrase_character_limit": "Max 50 characters", "passphrase_disabled_dialog_desc": "Enable Passphrase on your device to create hidden wallets.", "passphrase_disabled_dialog_title": "Can’t create hidden wallet", "prime.about_cloud_sync": "About cloud sync", "prime.about_cloud_sync_description": "Learn what data is synced and how your privacy is protected.", "prime.about_cloud_sync_included_data_title": "Included data", "prime.about_cloud_sync_security_title": "Security", "prime.about_onekey_prime": "About OneKey Prime", "prime.agree_to_terms": "Continuing means you agree to <link>OneKey Prime Terms</link>.", "prime.agree_to_terms_privacy": "By subscribing to OneKey Prime you agree to the <termsTag>OneKey Prime Terms</termsTag> and <privacyTag>Privacy Policy</privacyTag>.", "prime.app_data": "App data", "prime.auto_lock_description": "Enable OneKey Cloud, the auto-lock settings cannot use the \"Never\" and \"if away for 4 hrs\" option.", "prime.bulk_copy_addresses_desc": "Quickly select or generate addresses for bulk copying.", "prime.change_backup_password": "Change OneKey ID password", "prime.change_email": "Change email", "prime.change_password": "Change password", "prime.cloud_data_found": "Cloud data found", "prime.cloud_data_found_desc": "We found your previous cloud backup data, turn on will merge both data.", "prime.cloud_data_found_merge": "<PERSON><PERSON>", "prime.cloud_data_found_sync_device": "<PERSON><PERSON>", "prime.cloud_data_found_sync_time": "Sync time", "prime.cloud_sync_security_feature_five": "Completely open source and auditable", "prime.cloud_sync_security_feature_four": "12-character strong password required", "prime.cloud_sync_security_feature_one": "AES 256-bit encryption algorithm", "prime.cloud_sync_security_feature_three": "Local encryption, server zero-knowledge", "prime.cloud_sync_security_feature_two": "PBKDF2 Key Derivation (Anti-Brute Force)", "prime.code_resend": "Resend", "prime.confirm_password": "Confirm password", "prime.confirm_password_description": "Before you change password. We need to verify the current password of OneKey ID.", "prime.current": "Current", "prime.currently_email": "Currently: {currentlyemail}", "prime.custom_token_n_network": "Custom Token & Network", "prime.description": "Unlock advanced features to enhance your crypto asset management experience.", "prime.device_active_time_today": "Today", "prime.device_active_time_x_days_ago": "{number} days ago", "prime.device_active_time_x_min_ago": "{number} mins ago", "prime.device_active_time_yesterday": "Yesterday", "prime.device_limit_desc": "You can use OneKey Prime on up to 5 devices simultaneously.", "prime.device_limit_reached": "Device limit reached", "prime.device_limit_reached_desc": "For account security, you can log in to OneKey ID on up to 5 devices. Deactivate one to continue.", "prime.device_management": "Logged-in devices", "prime.device_management_desc": "Access Prime on up to 5 devices.", "prime.email_changed": "Email changed", "prime.end_date": "Ends on {data}", "prime.end_to_end_encryption_protection": "End-to-End Encryption Protection", "prime.end_to_end_encryption_protection_description": "Your data is encrypted locally with your OneKey ID password before being uploaded to the cloud. Only you can decrypt it — not even OneKey.", "prime.enter_verification_code": "Enter verification code", "prime.enter_verification_code_from_other_device": "Please enter the verification code from your other device", "prime.enter_verification_code_on_other_device": "Please enter the verification code on your other device to continue", "prime.enter_your_password": "Enter OneKey ID password", "prime.error_passcode_not_match": "Password do not match", "prime.error_passcode_too_long": "Password cannot exceed {length} characters", "prime.error_passcode_too_short": "Password must be at least {length} characters", "prime.error_passcode_too_short_12": "Password must be at least 12 characters", "prime.features_bulk_copy_detail_one_desc": "Manage all your blockchain addresses in one convenient place.", "prime.features_bulk_copy_detail_one_title": "Works with Bitcoin, Ethereum, and more", "prime.features_bulk_copy_detail_three_desc": "Download everything, or just the addresses you want.", "prime.features_bulk_copy_detail_three_title": "Export what you need", "prime.features_bulk_copy_detail_two_desc": "Easily export your crypto addresses — no tech skills needed.", "prime.features_bulk_copy_detail_two_title": "Supports all your wallets", "prime.features_bulk_revoke_detail_one_desc": "Use with your regular or hardware wallet. Remove any risky approvals in one click.", "prime.features_bulk_revoke_detail_one_title": "Supports all your wallets", "prime.features_bulk_revoke_detail_two_desc": "If a transaction is too expensive, we'll skip it to save you money.", "prime.features_bulk_revoke_detail_two_title": "Saves you gas fees", "prime.features_device_management_detail_one_desc": "Seamlessly access Prime on your phone, tablet, and desktop computer.", "prime.features_device_management_detail_one_title": "Multi-Platform support", "prime.features_device_management_detail_two_desc": "One-click switch, easily manage authorized devices.", "prime.features_device_management_detail_two_title": "Easy device management", "prime.features_onekey_cloud_detail_one_desc": "Use OneKey on your phone or computer — everything stays up to date automatically.", "prime.features_onekey_cloud_detail_one_title": "Your data, always in sync", "prime.features_onekey_cloud_detail_two_desc": "Wallet names, saved addresses, custom tokens — all synced for you.", "prime.features_onekey_cloud_detail_two_title": "Syncs what matters", "prime.features_title": "Prime features", "prime.forget_password": "Forget password?", "prime.i_understand": "I understand", "prime.incorrect_password": "Incorrect password", "prime.invalid_verification_code": "Invalid verification code", "prime.last_update": "Last update", "prime.last_update_today": "Today", "prime.last_update_x_min_ago": "{number} min ago", "prime.last_update_yesterday": "Yesterday", "prime.log_out": "Log out", "prime.log_out_device": "Log out on {DeviceName}?", "prime.log_out_device_desc": "This device will be logged out on next use.", "prime.manage_service": "Manage service", "prime.manage_subscription": "Manage subscription", "prime.manage_your_onekeyid": "Manage your OneKey ID.", "prime.membership_expired_sync_paused": "Prime membership has expired, and new content sync is paused. Your existing data remains secure. Reactivate Prime to resume.", "prime.monthly": "Monthly", "prime.new_email_code": "Please enter your new email address. We'll send you a verification code to confirm it.", "prime.new_password": "New Password", "prime.new_password_description": "You are resetting new password for: {email}", "prime.no_purchases_found": "No purchases found", "prime.not_logged_in_description": "Please log in to OneKey ID to use this feature", "prime.not_logged_in_title": "Not logged in", "prime.not_subscribed_description": "Please subscribe to OneKey Prime to use this feature", "prime.not_subscribed_title": "Not Subscribed", "prime.onekey_cloud_desc": "Automatically back up app usage data and synchronize seamlessly across devices.", "prime.onekeyid_been_log_out": "OneKey ID log out", "prime.onekeyid_been_log_out_desc": "This device been deactivated from another device. If this wasn't you, please check your email security.", "prime.onekeyid_continue": "Continue with OneKey ID", "prime.onekeyid_continue_description": "OneKey ID is all you need to access all OneKey services and earn referral rewards.", "prime.onekeyid_email_error": "Incorrect email address", "prime.onekeyid_log_out": "OneKey ID log out", "prime.onekeyid_log_out_description": "Log out may affect services related to OneKey ID, still want to log out?", "prime.onekeyid_signup": "Sign up OneKey ID", "prime.onekeyid_signup_description": "{email} is not registered yet, we will create a new account for you.", "prime.password": "Password", "prime.password_as_key_warning": "Your OneKey ID password is your encryption key. If you forget it, your data cannot be recovered.", "prime.password_characters": "At least 8 characters", "prime.password_letter": "At least 1 letter", "prime.password_level_good": "Good", "prime.password_level_strong": "Strong", "prime.password_level_weak": "Weak", "prime.password_number": "At least 1 number", "prime.password_placeholder": "Password", "prime.password_special_characters": "At least 1 special character", "prime.payment_cancelled": "Payment cancelled", "prime.payment_successful": "Payment successful", "prime.payment_successful_description": "Thank you for your purchase, May OneKey Prime helps you achieve a safer and more premium experience.", "prime.per_month": "Month", "prime.prime_price_per_month": "{price} / mo", "prime.prime_price_per_year": "{price} per year", "prime.reset": "Reset", "prime.reset_backup_password_checkbox_label": "I understand this will permanently delete my cloud backup data", "prime.reset_backup_password_description": "Resetting the OneKey ID password will delete existing backup data in the cloud and build a new backup from this device.", "prime.reset_backup_password_title": "Reset OneKey ID password", "prime.restore_purchases": "Restore purchases", "prime.restore_successful": "Successfully restored", "prime.restoring_previous_purchases": "Restoring previous purchases", "prime.save_discount": "Save {discount}%", "prime.send_code": "Send code", "prime.sent_to": "Sent to {email}", "prime.set_up_backup_password": "Set up OneKey ID password", "prime.set_up_backup_password_description1": "Set a password to encrypt your data for secure cross-device access.", "prime.set_up_backup_password_description2": "This password cannot be viewed or recovered by OneKey. If lost, cloud data cannot be restored (local data remains unaffected). Keep it safe.", "prime.signup_login": "Sign up / Login", "prime.status_free": "Free", "prime.status_prime": "Prime", "prime.strong_password": "Create a strong password", "prime.strong_password_desc": "12 character minimum.", "prime.subscribe": "Subscribe", "prime.subscribe_monthly_price": "Subscribe for {price}/month", "prime.subscribe_yearly_price": "Subscribe for {price}/year", "prime.subscription_auto_renew_price_month": "Auto-renews for {price}/month until canceled", "prime.subscription_auto_renew_price_year": "Auto-renews for {price}/year until canceled", "prime.subscription_manage_app_store": "You can manage or cancel your subscription in your App Store account settings.", "prime.sync_password_invalid_description": "Your OneKey ID password has been modified or reset by another device. Please re-enable OneKey Cloud and verify your OneKey ID password.", "prime.sync_password_invalid_title": "Invalid OneKey ID password", "prime.time_error_description": "System time error. OneKey Cloud cannot function properly. Please set your device to the correct time.", "prime.time_error_title": "Time error", "prime.transfer_description": "Manually transfer recovery phrases and private keys between devices.", "prime.update_email_address": "Update Email address", "prime.update_email_address_description": "To update your email address, please verify the currently Email first.", "prime.verification_code": "Verification code", "prime.verify_backup_password": "Verify OneKey ID password", "prime.verify_passcode_change_sync_password": "Please verify passcode to change OneKey ID password", "prime.verify_passcode_enable_cloud": "Please verify passcode to enable OneKey Cloud", "prime.verify_passcode_reset_sync_password": "Please verify passcode to reset OneKey ID password", "prime.wallet": "Wallet", "prime.wallet_diff_cloud": "Cloud", "prime.wallet_diff_confirm_desc": "The following wallets differ from the cloud, Please choose which name to keep.", "prime.wallet_diff_local": "Local", "prime.wallet_dup_confirm_desc": "The following wallets are duplicates; you can only keep one, the others will be removed.", "prime.wallet_list": "Wallet list", "prime.wallet_list_description": "Wallets / Accounts name", "prime.welcome_back": "Welcome back", "prime.what_data_included": "What data included", "prime.what_data_included_description": "Recovery phrases and private keys NOT included.", "prime.what_data_included_description_long": "Private data always stays on your device and isn't synced, like recovery phrases and private keys.", "prime.yearly": "Yearly", "protection_passwordless_nostr_signing_text": "Nostr signing without passcode", "provider.amount_received": "{amount} {token}", "provider.amount_required": "Insufficient input amount", "provider.approval_require": "Need to approve token before swap", "provider.cow_route": "CoW Swap is a meta DEX aggregator that uses intents and batch auctions to provide users with the best prices for their trades while protecting them from maximal extractable value (MEV).", "provider.fee": "$0.16 ", "provider.ios_popover.approval_require_msg": "Need to approve token before swap", "provider.ios_popover.approval_require_title": "Approval Required", "provider.ios_popover.onekey_fee": "OneKey fee", "provider.ios_popover.onekey_fee_content": "OneKey charges a {num} fee (limited-time discount), which is already included in the quote, supporting us in providing a high-quality, seamless experience.", "provider.ios_popover.onekey_fee_content_2": "Please note that slippage or network fees may affect the actual amount you receive. If you have any concerns or questions, please contact us.", "provider.ios_popover.onekey_fee_content_sub": "Please note that bridge fees, slippage, and floating rates may affect the final amount received. If you have any concerns about the fee, please contact us.", "provider.ios_popover.order_info_title": "Order Info", "provider.ios_popover.title": "Information", "provider.label_anti_mev": "Anti-MEV", "provider.label_approved": "Approved", "provider.label_fastest": "Fastest", "provider.label_max_received": "<PERSON> received", "provider.label_min_fee": "Min gas", "provider.label_overall_best": "Overall best", "provider.max_amount_required": "Max swap amount is {amount} {token}", "provider.min_amount_required": "Min amount requires {amount} {token}", "provider.network_fee": "Est. network fee", "provider.popover_fee_rate": "Fee rate", "provider.popover_order_info_content": "OneKey aggregates the best offers. Here’s what you should know before trading", "provider.popover_wallet": "Wallet", "provider.protocol_fee": "Fee charged by the provider", "provider.recommend": "Recommend", "provider.route": "Route", "provider.route_changelly_fix": "Please note that with the fixed rate, the expected received amount (excluding fees) will match the amount shown on the screen.", "provider.route_changelly_float": "Please be aware that the floating rate can change at any point due to market conditions, so you might receive more or less crypto than expected.", "provider.route_no_information": "The provider has temporarily not provided route information.", "provider.route_swft": "Please be aware that this transaction is utilizing a third-party SWFT_BRIDGE cross-chain bridge, which involves centralized execution and carries associated risks.", "provider.route_unable_fetch_price": "Failed to fetch the quote", "provider.sort_item_gas": "Gas Fee", "provider.sort_item_received": "Est. received", "provider.sort_item_swap_duration": "Swap Duration", "provider.sort_title": "Sort", "provider.swap_duration": "Est. swap duration", "provider.time": "< 1min", "provider.title": "Select provider", "provider.token_not_supported": "<PERSON><PERSON> not supported", "provider.unavailable": "Unavailable", "qr_wallet_address_creation_failed_dialog_title": "Address creation failed", "qr_wallet_address_creation_failed_firmware_update_desc": "If you can't create addresses for some networks, update your firmware via our update tool and disable Air-gap mode if it's on.", "qr_wallet_address_creation_failed_supports_network_desc": "QR wallet supports BTC, SOL, TRON and ETH + EVM networks.", "receive.address_unconfirmed_alert_message": "Address unverified", "receive.hide_unverified_address": "Hide unverified address", "receive.send_asset_warning_message": "Send only {network} network assets to this address", "receive.show_address_any": "Show address anyway", "receive_token_list_footer_text": "Can't find token? Try searching", "recovery_phrase_screenshot_protected_desc": "For your asset security, your recovery phrase will <tag>never appear in screenshots</tag>.", "recovery_phrase_screenshot_protected_title": "Recovery phrase protected", "referral.address_update_desc": "A code has been sent to {mail}, please verify before updating the address.", "referral.address_updated": "Receiving address updated", "referral.cumulative_rewards": "Cumulative rewards", "referral.distributed": "Distributed", "referral.earn_reward": "DeFi reward", "referral.earn_reward_desc": "Get commission from your friends’ <PERSON><PERSON><PERSON> rewards.", "referral.earn_reward_details": "Reward details", "referral.earn_reward_details_desc": "All tokens are snapshotted to USDC at market value on the 1st of each month, then distributed.", "referral.earn_reward_tips": "Rewards will be distributed to your wallet by the 10th of next month.", "referral.faqs": "FAQs", "referral.faqs_a1": "Invite friends to use your referral code for DeFi or hardware wallet purchases to get rewards. Rewards will be sent to your reward address about a month after settlement.", "referral.faqs_a2": "To prevent abuse, rewards can be claimed approximately 30 days later. Before that, you can click on the corresponding module to view details.", "referral.faqs_a3": "No manual claim needed. Eligible rewards will be snapshotted on the 1st of the next month and sent to your address by the 10th.", "referral.faqs_q1": "How do I join in the referral program?", "referral.faqs_q2": "Why haven't the rewards updated?", "referral.faqs_q3": "When can I claim the reward?", "referral.friends_address": "Friend’s address", "referral.how_1": "Create a OneKey ID", "referral.how_2": "Invite your friend by your referral code", "referral.how_3": "They buy hardware wallet or bind wallet with your code", "referral.how_4": "Your get reward", "referral.hw_level_rebate_rate": "{percent} reward", "referral.hw_level_title": "Level", "referral.hw_level_up_diamond": "Reached the highest level", "referral.hw_level_up_remain": "{Amount} more to {LevelName} level", "referral.hw_sales_title": "Monthly sales", "referral.intro_for_you": "For You", "referral.intro_for_you_1": "Get up to {RebateRate} hardware wallet sale commission", "referral.intro_for_you_2": "Unlock unlimited rewards from your friends' on-chain activity", "referral.intro_for_your_friend": "For Your Friend", "referral.intro_for_your_friend_1": "{RebateAmount} discount on hardware wallets at checkout with your code", "referral.intro_for_your_friend_2": "DeFi referral bonus", "referral.intro_learn_more": "Learn more", "referral.intro_title": "Invite Friends & Earn {RewardAmount}, Plus Lifetime Rewards!", "referral.intro_title_2": "How it works", "referral.invalid_code": "Invalid referral code", "referral.more_questions": "For more questions, check <url>https://help.onekey.so/articles/11461266<underline>here</underline></url>.", "referral.next_distribution": "Next distribution", "referral.next_distribution_day": "By {Month} 10", "referral.no_reward": "No reward yet", "referral.onboard_bind_code": "Bind referral code", "referral.onboard_bind_code_finish": "Finish", "referral.order_info": "Order info", "referral.order_reward": "<PERSON><PERSON>", "referral.referred": "Referred", "referral.referred_address": "Address", "referral.referred_empty": "Start earning today", "referral.referred_empty_desc": "Share your referral code to start earning rewards", "referral.referred_total": "Total", "referral.referred_total_IDs": "Total IDs", "referral.referred_total_addresses": "Total addresses", "referral.referred_total_orders": "Total orders", "referral.referred_total_wallets": "Total Wallets", "referral.referred_type_2": "<PERSON><PERSON><PERSON>", "referral.referred_type_3": "Hardware sales", "referral.reward_edit_address": "Edit address", "referral.reward_edit_address_desc_1": "On the 1st of each month, a snapshot will be taken of eligible rewards and the current address. ", "referral.reward_edit_address_desc_2": "Rewards will be sent to the address by the 10th of the same month.", "referral.reward_edit_address_error": "EVM address only", "referral.reward_empty": "Start earning today", "referral.reward_empty_desc": "Share your referral code to start earning rewards", "referral.reward_history": "Reward distribution history", "referral.reward_history_reward_title": "<PERSON><PERSON>", "referral.reward_received_address": "Receiving address", "referral.reward_received_address_notset": "Not yet set", "referral.reward_undistributed": "Undistributed Reward", "referral.reward_undistributed_pending": "Pending", "referral.sales_level_bronze": "Bronze", "referral.sales_level_silver": "Silver", "referral.sales_level_upgrade": "(Upgrade coming soon)", "referral.sales_order_pending": "Pending", "referral.sales_order_refund": "Sales refund", "referral.sales_order_regular": "Sales reward ", "referral.sales_order_unknown": "Unknown", "referral.sales_reward": "Hardware sale reward", "referral.sales_reward_desc": "Your friend gets {SalesDiscount} off, you get at least {RebateRate} reward.", "referral.sales_reward_desc_limit": "(Time-limited)", "referral.sales_reward_pending": "Pending", "referral.sales_reward_tips": "Rewards will be distributed about 30 days after order completion.", "referral.swap_reward": "Swap reward", "referral.swap_reward_desc": "Get commission when your friend makes a Swap.", "referral.title": "Invite & Reward", "referral.total_reward": "Total Reward", "referral.total_reward_pop1": "Total confirmed rewards for all referrals", "referral.total_reward_pop2": "Pending rewards are not included", "referral.undistributed": "Undistributed", "referral.wallet_bind_code_finish": "Bounded", "referral.wallet_code_desc": "Once bound, all addresses derived from this wallet will be associated with this referral code and cannot be changed.", "referral.wallet_code_placeholder": "Referral code", "referral.wallet_code_title": "Do you have a referral code?", "referral.wallet_code_wallet": "Bind to", "referral.wallet_edit_code": "Referral code", "referral.your_code": "Referral code", "referral.your_referred": "Your referred", "referral.your_referred_wallets_1address": "1 address", "referral.your_referred_wallets_details": "Details", "referral.your_referred_wallets_more_address": "{amount} addresses", "remove_account_desc": "This account will be removed.", "remove_device": "Remove device", "remove_device_desc": "This will disconnect this hardware wallet from the OneKey App. You can reconnect it to the App whenever you want.", "remove_hidden_wallet": "Remove Hidden Wallet", "remove_hidden_wallet_desc": "Remove this hidden wallet? Make sure you have saved the passphrase.", "remove_private_key_account_desc": "You can restore the account using its private key after removal. Ensure it's backed up to avoid permanent loss of access.", "remove_standard_wallet": "Remove standard wallet", "remove_standard_wallet_desc": "Your funds and transactions won't be visible until you connect your device and re-create a standard wallet.", "remove_wallet": "Remove wallet", "remove_wallet_desc": "Make sure you've written down the recovery phrase before removing the wallet. Otherwise, you won't be able to recover the wallet.", "remove_wallet_double_confirm_message": "I've written down the recovery phrase", "reset_app_desc": "This will delete all the data you have created on OneKey. After making sure that you have a proper backup, enter \"RESET\" to reset the App", "scan.camera_access_denied": "Camera access denied", "scan.enable_camera_permissions": "OneKey requires camera access to scan QR codes. Please go to “Settings” and enable camera permissions to use this feature.", "scan.grant_camera_access_in_expand_view": "Please grant camera access in the expand view.", "scan.move_closer_if_scan_fails": "If the scan fails, adjust the device-camera distance and retry", "scan.no_recognizable_qr_code_found": "No recognizable QR code found.", "scan.qr_wallet_detected": "QR wallet detected. Go to connect this wallet?", "scan.scan_address_codes_to_copy_address": "Scan address codes to copy address", "scan.scan_qr_code": "Scan QR code", "scan.scan_qr_code_on_device": "Scan QR code on device", "scan.scan_walletconnect_code_to_connect_to_sites": "Scan WalletConnect code to connect to sites", "scan.screen_blurred_for_security": "We've blurred your screen for security, but it won't affect your scan", "scan.select_a_photo": "Select a photo", "scan.show_qr_code_steps": "To show your QR code, go to Connect App Wallet > QR Code > OneKey Wallet on your device", "scan_qr_code_to_verify_details": "Scan the QR code with your device to verify the details.", "scan_to_create_an_address": "Scan to create an address", "scanning_text": "Scanning", "secure_qr_toast_scan_qr_code_on_device_text": "Return when the QR code shows, click 'Next', then scan it.", "select_recovery_phrase_length": "Select a length", "selected_network_only_supports_device": "The selected network currently only supports {deviceType}", "send.address_exists": "The address already exists", "send.address_invalid": "Invalid address. Please check and re-enter.", "send.address_not_allowlist_error": "Transfer allowlist enabled. This address is not on the address book. Please <action>to_add_address_page<underline>add it</underline></action> to proceed.", "send.amount": "Amount", "send.amount_invalid": "Insufficient balance. Please try a smaller amount", "send.amount_too_small": "Transfer amount too small.", "send.authentication_failed_verify_again": "Authentication failed, please check your network connection and try again.", "send.available": "Available: {number}", "send.balance": "Balance: {number}", "send.cannot_send_amount_zero": "Cannot send 0 amount", "send.cannot_send_to_self": "Cannot send to yourself", "send.check_request_error": "Check request error, please refresh again", "send.contract_address_detected_warning": "Contract address detected, funds maybe loss", "send.description_balance_info": "Balance does not include locked or small UTXOs", "send.description_frozen_funds_info": "Balance does not include frozen funds", "send.engine_account_not_activated": "Account not activated.", "send.engine_failed_to_transfer": "Transfer failed.", "send.engine_incorrect_address": "Incorrect address.", "send.engine_incorrect_passcode": "Incorrect passcode", "send.engine_incorrect_password": "Incorrect password.", "send.engine_incorrect_token_address": "Incorrect token address.", "send.engine_incorrect_transfer_value": "Incorrect transfer amount.", "send.engine_internal_error": "Service error.", "send.engine_not_implemented": "Function not yet implemented.", "send.engine_passcode_not_set": "Passcode not set", "send.engine_password_not_set": "Password not set.", "send.engine_pending_queue_too_long": "Too many pending transactions in the queue.", "send.engine_too_many_derived_accounts": "Number of accounts exceeds the limit.", "send.ens_choose_address_title": "Choose an Address", "send.error_insufficient_balance": "Insufficient {token} balance", "send.error_minimum_amount": "Minimum amount {amount} {token}", "send.error_recipient_requires_activation": "Recipient requires {amount} {token} to activate", "send.estimated_gas_failure": "Gas estimation failed.", "send.field_large_than": "Must be greater than {0}.", "send.field_only_integer": "Must be a positive integer.", "send.field_too_large": "Cannot be greater than {0}.", "send.field_too_small": "Must not be less than {0}.", "send.hex_data_contract_interaction_warning": "This contract interaction is triggered by the hex data ", "send.hex_data_operations_warning": "You're granting another address access to your tokens. Only proceed if you fully trust it.", "send.hex_data_user_understand_risk": "I understand the risks and want to proceed", "send.insufficient_liquidity_of_lightning_node_channels": "Insufficient liquidity in Lightning node channels.", "send.invalid_address_ordinal_can_only_be_sent_to_taproot_address": "Invalid address! Ordinal assets can only be sent to Taproot addresses.", "send.invalid_lightning_payment_request": "Invalid Lightning payment request.", "send.invoice_is_already_paid": "Invoice is already paid.", "send.label_cex": "You are depositing to a centralized exchange address. Please double-check the selected network and address.", "send.label_cex_title": "CEX", "send.label_contract_address": "The contract address is labeled {name}.", "send.label_contract_address_title": "Contract address", "send.label_first_transfer": "Initial transfer", "send.label_scam": "You are sending to a scam address, which could result in a loss of your assets. Please double-check the address and be aware of any risks.", "send.label_scam_title": "Scam address", "send.label_transferred": "Transferred", "send.label_wallet_name": "Wallet name / Account name", "send.max": "Max", "send.memo_up_to_length": "Up to {number} characters", "send.nft_amount": "Amount", "send.nft_does_not_exist": "NFT does not exist.", "send.no_route_found": "No route found.", "send.no_token_message": "No tokens were found at this address", "send.passcode_validation": "Passcode must be between 8 and 128 characters.", "send.password_validation": "Password must be between 8 and 128 characters.", "send.preview_button": "Preview", "send.recipient_invalid": "Invalid recipient. Please check and re-enter", "send.send_to_this_address": "Send to this address", "send.sending_str_requires_an_account_balance_of_at_least_str_str": "Sending {0} requires an account balance of at least {1} {2}.", "send.str_minimum_balance_is_str": "Sending failed. Minimum balance for {token} is {amount}.", "send.str_minimum_transfer": "Minimum transfer amount is {0}.", "send.suggest_reserving_str_as_gas_fee": "Suggest reserving {0} as a network fee.", "send.tag": "Tag", "send.tag_placeholder": "Comment, tag, memo or note", "send.the_invoice_has_expired": "Invoice has expired.", "send.the_minimum_value_for_transferring_to_a_new_account_is_str_str": "The minimum value for transferring to a new account is {amount} {symbol}.", "send.title": "Send", "send.to": "To", "send.to_contacts_selector_account": "My account", "send.to_contacts_selector_account_title": "Select", "send.to_contacts_selector_address_book": "Address book", "send.to_contacts_tooltip": "Contacts", "send.to_ln_placeholder": "Enter Invoice, Lightning Address, LNURL or Node ID", "send.to_paste_tooltip": "Paste", "send.to_placeholder": "Enter address or domain name", "send.to_scan_tooltip": "<PERSON><PERSON>", "send.toast_btc_fork_insufficient_fund": "Insufficient balance. Please try a smaller amount", "send.transferred_address_add": "Previously transferred to this address, but it's not in the address book. Consider <action>to_edit_address_book_page<underline>adding it</underline></action>.", "send.unrecognized_hex_data_risky_warning": "Unrecognized hex data may be risky. Only proceed if you fully trust it.", "send.verification_failure": "Verification failed.", "send_token_selector.no_match_found": "No match found for your search.", "send_token_selector.no_results": "No Results", "send_token_selector.search_placeholder": "Search symbol or contract address", "send_token_selector.select_token": "Select Token", "sending_krc20_warning_text": "Sending KRC20 tokens requires two confirmations on your hardware. Do not cancel the second confirmation, or the KAS sent in the first confirmation will be difficult to recover.", "setting.floating_icon": "Floating icon", "setting.floating_icon_always_display": "Always display", "setting.floating_icon_always_display_description": "When enabled, the OneKey icon floats on the webpage edge, helping you quickly check dApps' security information.", "setting.introducing_floating_icon": "Introducing floating icon", "settings.account_derivation_path": "Account derivation path", "settings.account_derivation_path_desc": "If you don’t see the accounts you expect, try switching the derivation path.", "settings.account_sync_dapp_to_wallet_mode_description": "Automatically syncs your dApp account and network settings to your wallet when switching back from a dApp.", "settings.account_sync_dapp_to_wallet_mode_title": "Align dApp account to wallet", "settings.account_sync_independent_mode_description": "Wallet and dApp accounts operate independently, without interfering with each other.", "settings.account_sync_independent_mode_title": "Independent mode", "settings.account_sync_modal_title": "Wallet & dApp account alignment", "settings.account_sync_wallet_to_all_mode_description": "Your current wallet account will be used for all dApp interactions.", "settings.account_sync_wallet_to_all_mode_title": "Always use wallet account", "settings.address_book": "Address book", "settings.app_update_available": "App update available", "settings.app_update_cache": "App update cache", "settings.auto_lock": "Auto-lock", "settings.backup_recovery_phrase_to_onekey_keytag": "Backup your recovery phrase to OneKey KeyTag", "settings.backup_recovery_phrase_to_onekey_lite": "Backup your recovery phrase to <PERSON><PERSON><PERSON>", "settings.backup_with_onekey_keytag": "Back up with <PERSON><PERSON><PERSON>", "settings.browser_cache": "Browser cache", "settings.browser_history_bookmarks_pins_risk_dapp_whitelist": "Browser history, bookmarks, pins, risk dApp whitelist", "settings.change_pin": "Change PIN", "settings.clear_browser_cache": "Clear browser cache", "settings.clear_browser_cache_desc": "Open this link in your browser to clear the cache: {url}", "settings.clear_browser_cache_desc2": "Please go to the browser settings page to continue clearing.", "settings.clear_cache_on_app": "Clear cache on App", "settings.clear_data": "Clear data", "settings.clear_pending_transactions": "Clear pending transactions", "settings.clear_pending_transactions_desc": "Clear the pending data in the local history record.", "settings.clear_successful": "Clear successful", "settings.connected_sites": "Connected sites", "settings.contact_us": "Contact us", "settings.create_remove_wallets": "Create / remove wallets without passcode", "settings.create_remove_wallets_desc": "No passcode needed for creating/removing wallets", "settings.create_transactions": "Create transaction without passcode", "settings.create_transactions_desc": "No passcode needed for transactions", "settings.cryptocurrency": "Cryptocurrency", "settings.default_currency": "Default currency", "settings.default_wallet_settings": "Default wallet settings", "settings.export_network_config_custom_network_label": "EVM networks and tokens", "settings.export_network_config_desc": "Export your custom network configurations from OneKey 4.0.", "settings.export_network_config_label": "Export custom network config", "settings.export_state_logs": "Export state logs", "settings.export_state_logs_desc": "This will help us debug any issue you might encounter. Please send to {email} to contact OneKey support.", "settings.fiat": "Fiat", "settings.go_to_settings_page_now": "Go to the Settings Page Now?", "settings.google_drive_backup": "Google Drive backup", "settings.haptic_feedback": "Haptic feedback", "settings.hardware_bridge_status": "Hardware bridge status", "settings.hardware_wallets_not_appear": "Hardware wallets will not appear here.", "settings.help_center": "Help center", "settings.how_to_import_from_onekey_keytag": "How to import from OneKey KeyTag？", "settings.how_to_import_from_onekey_keytag_desc": "Sum the numbers in each row. This sum represents the word's position in the word list.\nThen, visit the {dotmap} website to find the corresponding word for this position.", "settings.icloud_backup": "iCloud backup", "settings.if_away_for_1_hr": "If away for 1 hr", "settings.if_away_for_1_min": "If away for 1 min", "settings.if_away_for_2_hrs": "If away for 2 hrs", "settings.if_away_for_30_mins": "If away for 30 mins", "settings.if_away_for_4_hrs": "If away for 4 hrs", "settings.if_away_for_5_mins": "If away for 5 mins", "settings.import_recovery_phrase_from_onekey_keytag": "Import recovery phrase from your OneKey KeyTag", "settings.import_recovery_phrase_from_onekey_lite": "Import recovery phrase from your OneKey Lite", "settings.lock_now": "Lock now", "settings.logs_do_not_include_sensitive_data": "Logs do not include sensitive data like recovery phrases or private keys. They only contain local data, crash reports, and public wallet addresses.", "settings.migration": "Migration", "settings.nfc_not_supported": "Your current device does not support NFC, replace it with an NFC-enabled device and try again.", "settings.no_connected_sites": "No connected sites", "settings.no_connected_sites_desc": "All sites connected through OneKey will appear here.", "settings.no_signed_text": "No signed text", "settings.no_signed_text_desc": "All text signed through OneKey will appear here.", "settings.no_signed_transactions": "No signed transactions", "settings.no_signed_transactions_desc": "All transactions signed through OneKey will appear here.", "settings.onekey_keytag_desc": "Powerful wallet backup kit made of titanium alloy", "settings.onekey_lite_desc": "Restore your wallet without typing one word.", "settings.onekey_wants_to_use_nfc": "\"OneKey\" wants to use NFC", "settings.onekey_wants_to_use_nfc_desc": "To connect to OneKey Lite, NFC permissions are required.", "settings.passcode_bypass": "Passcode bypass", "settings.passcode_bypass_desc": "When exceeding the allowed time of 2 hours, passcode verification is still required to ensure security.", "settings.passkey": "PassKey", "settings.password_bypass": "Password bypass", "settings.password_bypass_desc": "When exceeding the allowed time of 2 hours, password verification is still required to ensure security.", "settings.privacy_policy": "Privacy policy", "settings.protection": "Protection", "settings.protection_passcode_section_title": "Passcode protection", "settings.rate_app": "Rate the App", "settings.reset": "Reset", "settings.reset_app": "Reset App", "settings.reset_app_desc": "This will delete all data on OneKey. Ensure you have a backup, then enter \"RESET\" to proceed.", "settings.reset_app_description": "Reset app on this device after 10 failed passcode attempts.", "settings.resources": "Resources", "settings.search_title": "Search results for \"{keyword}\"", "settings.select_wallet": "Select wallet", "settings.set_auto_lock_duration_desktop": "When the app runs in the background without exiting or when the device screen is locked, it will automatically lock after a specified period of time.", "settings.set_auto_lock_duration_extension": "When closing the plugin window or when in full-screen mode with unused tabs, it will automatically lock after a specified period of time.", "settings.set_auto_lock_duration_mobile": "When the app runs in the background without exiting or when the device screen is locked, it will automatically lock after a specified period of time.", "settings.set_auto_lock_duration_web": "When the app is in an unused tab or minimized window, it will automatically lock after a specified period of time.", "settings.settings": "Settings", "settings.shortcuts": "Shortcuts", "settings.sign_text": "Sign text", "settings.signature_record": "Signature record", "settings.spend_dust_utxo": "Spend dust UTXO", "settings.spend_dust_utxo_desc": "Using dust UTXO increases transaction fees and reduces privacy. It's recommended to disable this feature to avoid malicious tracking.", "settings.step1_get_bip39_dotmap": "Step 1: get your BIP39 dotmap", "settings.step1_get_bip39_dotmap_desc": "Visit the {dotmap} online or refer to the physical map in your KeyTag starter guide.", "settings.step2_match_recovery_phrase_dots": "Step 2: match recovery phrase dots", "settings.step2_match_recovery_phrase_dots_desc": "Locate the dot pattern for each word of your recovery phrase on the {dotmap} .", "settings.step3_align_and_punch": "Step 3: align and punch", "settings.step3_align_and_punch_desc": "Each line of KeyTag represents a word. Use a center punch tool for accurate punching based on the black circle's position.\nFor Passphrase backup, punch on the line marked with '*'.", "settings.submit_request": "Submit a request", "settings.swap_history": "Swap orders", "settings.system_idle_lock": "System idle lock", "settings.system_idle_lock_desc": "When enabled, starts timing when there is no user activity, even if the app is in the foreground", "settings.theme": "Theme", "settings.token_nft_data": "Token & NFT data", "settings.token_risk_protection": "Risk protection", "settings.token_risk_reminder": "Token risk reminder", "settings.token_risk_reminder_desc": "When enabled, you'll be reminded when selecting non-verified tokens.", "settings.transaction_history": "Transaction history", "settings.transactions": "Transactions", "settings.turn_on_nfc": "Turn on NFC and Let \"OneKey\" Connect Your Hardware Devices", "settings.unable_to_connect": "Unable to connect", "settings.update_available": "Update available", "settings.user_agreement": "User agreement", "settings.version_versionnum": "version {versionNum}", "settings.view_address_in_explorer": "View address in explorer", "settings.view_transaction_in_explorer": "View transaction in explorer", "settings.whats_new": "What’s new", "settings_protection.allowlist_content": "When enabled, you can only send funds to accounts within the wallet or to any address in the address book.", "settings_protection.allowlist_title": "Transfer allowlist", "shortcut.go_back": "Go back", "shortcut.go_forward": "Go forward", "shortcut.hide_sidebar": "Hide sidebar", "shortcut.show_sidebar": "Show sidebar", "shortcuts.account_selector": "Account selector", "shortcuts.close_current_tab": "Close current tab", "shortcuts.go_to_browser_tab": "Go to browser tab", "shortcuts.go_to_earn_tab": "Go to earn tab", "shortcuts.go_to_market_tab": "Go to market tab", "shortcuts.go_to_myonekey_tab": "Go to My OneKey tab", "shortcuts.go_to_referral_tab": "Go to Referral tab", "shortcuts.go_to_swap_tab": "Go to swap tab", "shortcuts.go_to_wallet_tab": "Go to wallet tab", "shortcuts.network_selector": "Network selector", "sidebar.refer_a_friend": "Referral", "sig.account_rent_label": "Account rent", "sig.approval_label": "Approval", "sig.approve_to_label": "Approve to", "sig.interact_contract_label": "Interact contract", "sig.revoke_approval_label": "Revoke approval", "sig.revoke_from_label": "<PERSON>oke from", "sig.signature_request_label": "Signature request", "sign.swap_estimate_receive": "Estimate received", "skip_verify_text": "I don't have my device with me", "slippage_tolerance.button_save": "Save", "slippage_tolerance.description": "Auto slippage optimizes slippage based on pool liquidity and trading volume, reducing transaction failures and MEV attack risks.", "slippage_tolerance.error_message": "Slippage Tolerance must be between 0 to 50%.", "slippage_tolerance.input_placeholder": "0.005", "slippage_tolerance.popover": "Slippage tolerance is a setting for the amount of price slippage you are willing to accept for a trade.", "slippage_tolerance.switch_auto": "Auto", "slippage_tolerance.switch_custom": "Custom", "slippage_tolerance.title": "Slippage tolerance", "slippage_tolerance.warning_message_1": "High slippage tolerance may cause your asset loss.", "slippage_tolerance.warning_message_2": "Slippage under {number}% may cause trade failure.", "speed_up_cancellation": "Speed up cancellation", "spotlight.account_alignment_desc": "OneKey now defaults to automatically switching to your last used dApp account. To change this setting, go to Settings > Wallet & dApp account alignment.", "spotlight.enable_account_asset_message": "If balances are missing, go to \"Networks\" on the homepage and select \"All networks\" first.", "spotlight.enable_network_message": "Manage network visibility and quickly copy addresses for any network in 'All networks' view.", "swap.approve_token": "Approve {num} {token}...", "swap.approve_token_est_time": "Est. in {num} s", "swap.btn_approving": "Approving...", "swap.btn_building": "Building order...", "swap.ch_status_hold": "Contact support", "swap.loading_content": "Calculating the best price...", "swap.native_token_max_tip": "Please reserve a small amount of tokens to pay for network fees, otherwise the transaction will fail.", "swap.native_token_max_tip_already": "To make sure your transaction goes through, {num_token} of your balance has been reserved to pay for the network fee. The reserved amount may vary due to onchain network fee.", "swap.network_cost_dialog_content": "The network fee for this order is high relative to the total order amount. You can proceed with the swap, but a significant portion may be consumed by network costs.", "swap.network_cost_dialog_description": "Network costs exceed {number} of the order amount", "swap.network_cost_dialog_title": "Order amount is too small", "swap.provider_manage_unavailable": "*The provider is temporarily unavailable.", "swap.revoke_successful": "Revoke approval successful", "swap.stablecoin_0_fee": "0 Fee", "swap.step_1": "1. ", "swap.toast_go_to_swap": "Go to swap", "swap.toast_go_to_swap_desc": "Approve {num} {token} for {provider}", "swap.toast_go_to_swap_desc_unlimited_approve": "Approve unlimited {token} to {provider}", "swap_account.from_address": "{addrss}", "swap_account.to_address": "{addrss}", "swap_account.to_address_edit": "(edited)", "swap_account.to_address_edit_button": "Reset", "swap_history.all_history": "All history", "swap_history.all_history_content": "This will clear all history in the local history record.", "swap_history.all_history_title": "Clear all history", "swap_history.amount_received": "+{amount} {token}", "swap_history.amount_sent": "-{amount} {token}", "swap_history.date_time_format": "{date} {time}", "swap_history.pending_history": "Pending history", "swap_history.pending_history_content": "This will clear all pending data in the local history record, but it will not affect the status of this transaction on the blockchain.", "swap_history.pending_history_title": "Clear pending history", "swap_history.status_canceled": "Canceled", "swap_history.status_cancelling": "Cancelling", "swap_history.status_discard": "Timeout", "swap_history.status_failed": "Failed", "swap_history.status_pending": "Pending", "swap_history.status_success": "Success", "swap_history.title": "Swap & Bridge", "swap_history.transaction_format": "{token_from} → {token_to}", "swap_history_detail.badge_bridge_failed": "Bridge transaction failed", "swap_history_detail.badge_bridge_pending": "Bridge transaction pending", "swap_history_detail.badge_bridge_success": "Bridge transaction successful", "swap_history_detail.badge_expired": "Order expired", "swap_history_detail.badge_from_failed": "Source chain transaction failed", "swap_history_detail.badge_from_pending": "Source chain transaction pending", "swap_history_detail.badge_from_success": "Source chain transaction successful", "swap_history_detail.badge_provider_error": "Provider error", "swap_history_detail.badge_refund_failed": "Refund failed", "swap_history_detail.badge_refunded": "Refunded", "swap_history_detail.badge_refunding": "Refunding", "swap_history_detail.badge_to_failed": "Target chain transaction failed", "swap_history_detail.badge_to_pending": "Target chain transaction pending", "swap_history_detail.badge_to_success": "Target chain transaction successful", "swap_history_detail.clear_content": "This will clear the data in the local history record.", "swap_history_detail.clear_title": "Clear history", "swap_history_detail.date": "Time", "swap_history_detail.delete_confirm": "Delete", "swap_history_detail.delete_title": "Are you sure to delete all history?", "swap_history_detail.from": "Pay network", "swap_history_detail.network_fee": "Network fee", "swap_history_detail.order_detail": "Status detail", "swap_history_detail.order_status": "Status", "swap_history_detail.pay_address": "Pay address", "swap_history_detail.protocol_fee": "Provider <PERSON>", "swap_history_detail.provider": "Provider", "swap_history_detail.rate": "Rate", "swap_history_detail.receive_amount": "+Receive Amount", "swap_history_detail.received_address": "Received address", "swap_history_detail.send_amount": "-Send Amount", "swap_history_detail.service_fee": "Service Fee", "swap_history_detail.status_pending": "Pending", "swap_history_detail.surplus": "Surplus", "swap_history_detail.swap_duration": "Swap duration", "swap_history_detail.title": "Transaction", "swap_history_detail.to": "Received nework", "swap_history_detail.toast_copy": "<PERSON>pied", "swap_history_detail.transaction_hash": "Transaction hash", "swap_history_detail.transaction_price": "Transaction Price", "swap_history_detail.view_in_browser": "View on Explorer", "swap_page.account": "Account {number}", "swap_page.account_to_address_title": "Edit address", "swap_page.alert.account_does_not_support_swap": "Connected account doesn't support swap. Try another.", "swap_page.alert.fee_exceeds_amount": "Est network fee exceeds swap amount, proceed with caution.", "swap_page.alert.fee_exceeds_amount_title": "Network fee alert", "swap_page.alert.maximum_amount": "Max amount {number} {symbol}", "swap_page.alert.minimum_amount": "Minimum amount {number} {symbol}", "swap_page.alert.network_fee_deducted": "Network fee in ETH deducted automatically in the next step.", "swap_page.alert.no_provider_supports_trade": "No provider supports this trade", "swap_page.alert.not_best_rate": "The current provider does not offer the best rate for this trade.", "swap_page.alert.require_native_token_content": "Some bridges charge extra source chain native tokens as a bridge fee.", "swap_page.alert.require_native_token_title": "Require {n} {token}", "swap_page.alert.tax_detected": "The tax for the token goes to the token project, not OneKey.", "swap_page.alert.tax_detected_buy": "buy", "swap_page.alert.tax_detected_sell": "sell", "swap_page.alert.tax_detected_title": "{percentage} {token} {action} tax detected", "swap_page.alert.value_drop": "High price impact may cause your asset loss.", "swap_page.alert.value_drop_title": "{number} value drop", "swap_page.amount": "{number}", "swap_page.approve_and_sign": "Approve and sign", "swap_page.approve_and_swap": "Approve and Swap", "swap_page.approve_button": "Approve {token}", "swap_page.balance": "Balance: {number}", "swap_page.bridge": "Bridge", "swap_page.button_approve_amount": "Approve {number}", "swap_page.button_approve_unlimited": "Approve unlimited", "swap_page.button_cross_chain": "Bridge", "swap_page.button_enter_a_recipient": "Enter a recipient", "swap_page.button_enter_amount": "Enter amount", "swap_page.button_fetching_quotes": "Fetching quotes...", "swap_page.button_insufficient_balance": "Insufficient balance", "swap_page.button_no_connected_wallet": "No wallet connected", "swap_page.button_no_enough_fee": "Not enough to cover network fee", "swap_page.button_no_liquidity": "No liquidity for this trade", "swap_page.button_refresh_quotes": "Refresh quotes", "swap_page.button_select_token": "Select token", "swap_page.button_wrap": "Swap via Wrap Contract", "swap_page.buy_sell_tax": "{token} token tax", "swap_page.create_to_enable_network": "Create to enable the network", "swap_page.from": "From", "swap_page.limit": "Limit", "swap_page.limit_dialog_button": "Confirm", "swap_page.limit_dialog_content": "Limit order will be available soon. If you have old orders on OneKey V4, you can visit Matcha (https://matcha.xyz) to view or manage your orders.", "swap_page.limit_dialog_title": "Limit order", "swap_page.max": "Max", "swap_page.no_address": "No {network} address", "swap_page.percentage_change": "(+{number}%)", "swap_page.price_impact_content_1": "Value difference = (Received value – Paid value) / Paid value", "swap_page.price_impact_content_2": "When a pool has low liquidity, your trade may experience greater price impact. When the value difference is too large, OneKey Swap will initiate a price impact warning to alert you to the potential for significant loss.", "swap_page.price_impact_title": "Value difference", "swap_page.provider.approve": "Approve", "swap_page.provider.approve_amount": "Approve amount", "swap_page.provider.approve_amount_limit": "{value} {token}", "swap_page.provider.approve_amount_un_limit": "Unlimited", "swap_page.provider.approve_usdt_dialog_content": "Since the currently approved USDT amount is lower than the intended amount, you must first revoke the existing approval before setting a new allowance.", "swap_page.provider.approve_usdt_dialog_title": "Insufficient approved amount", "swap_page.provider.custom": "Custom ({number}%)", "swap_page.provider.est_network_fee": "Est network fee", "swap_page.provider.exchange_rate": "1 ETH = {number} USDC", "swap_page.provider.fee_amount": "${number}", "swap_page.provider.provider": "Provider", "swap_page.provider.provider_insufficient_liquidity": "No provider support", "swap_page.provider.rate_unavailable": "Failed to fetch the quote", "swap_page.provider.slippage_auto": "Auto ({number}%)", "swap_page.provider.slippage_tolerance": "Slippage", "swap_page.recent_trade": "Recent trade", "swap_page.recipient_add": "Add recipient address", "swap_page.recipient_edit": "Edit recipient", "swap_page.recipient_external_account": "External account", "swap_page.recipient_modal_do_not": "Do not send to centralized exchange. Tokens sent to the wrong address are irretrievable", "swap_page.recipient_modal_verify": "Verify the address is accurate and fully compatible with the target network", "swap_page.recipient_send_to": "Sent to", "swap_page.settings": "Advanced settings", "swap_page.settings_recipient_content": "Allows you to choose a destination address for the swap other than the connected one", "swap_page.settings_recipient_title": "Custom recipient", "swap_page.settings_simple_mode": "Smart mode", "swap_page.settings_simple_mode_content": "Provide a better trading experience and ensure the security of your approval", "swap_page.swap": "<PERSON><PERSON><PERSON>", "swap_page.swap_button": "<PERSON><PERSON><PERSON>", "swap_page.swap_steps_1": "Approve {tokenSymbol}", "swap_page.swap_steps_1_approve_dialog": "Give the provider permission to swap that token from your wallet. ", "swap_page.swap_steps_2": "2. <PERSON><PERSON><PERSON>", "swap_page.to": "To", "swap_page.toast.address_generated": "Address created", "swap_page.toast.address_generated_fail": "Address creation failed", "swap_page.toast.approve_canceled": "Approve cancel", "swap_page.toast.approve_canceled_detail": "{token}", "swap_page.toast.approve_discarded": "Approve discarded", "swap_page.toast.approve_discarded_detail": "{token}", "swap_page.toast.approve_failed": "Approve failed", "swap_page.toast.approve_failed_detail": "{token}", "swap_page.toast.approve_successful": "Approve successful", "swap_page.toast.approve_successful_detail": "{token}", "swap_page.toast.insufficient_balance_content": "You should reserve at least {number} {token} to cover the network fee.", "swap_page.toast.insufficient_balance_title": "Insufficient {token} balance", "swap_page.toast.insufficient_input_amount": "Insufficient input amount", "swap_page.toast.swap_failed": "Swap failed", "swap_page.toast.swap_failed_detail": "{number} {tokenSymbol} → {number} {tokenSymbol}", "swap_page.toast.swap_successful": "Swap successful", "swap_page.toast.swap_successful_detail": "{number} {symbol} → {number} {symbol}", "swap_page.toast.taproot_unsupported": "ThorSwap does not currently support Taproot-format BTC address.", "swap_page.toast.token_not_supported": "Token is not supported", "swap_page.usd_value": "${number}", "swap_process.build_and_estimate_tx": "Build and estimate Tx", "swap_process.create_order": "Create order", "swap_process.sign_and_sent_tx": "Sign and send Tx", "swap_review.confirm_2_on_device": "Confirm twice on device", "swap_review.confirm_3_on_device": "Confirm three times on device", "swap_review.confirm_swap": "Confirm swap", "swap_review.min_receive": "<PERSON> received", "swap_review.min_receive_popover": "Minimum receive amount after slippage and fees, and the impact of floating rates (some centralized providers only).", "swap_review.network_cost_popover_content": "This is the cost to process your transaction on the blockchain. OneKey does not receive any share of these fees.", "swap_review.provider_popover_content": "External provider handling the exchange for the current order.", "swap_review.recipient_popover": "Swap and send tokens to another address.", "swap_review.review_swap": "Review swap", "swap_review.sign_and_submit": "Sign and submit transaction", "swap_review.transaction_failed": "Transaction failed", "swap_review.transaction_speed": "Transaction speed", "swap_review.transaction_succeeded": "Transaction successful", "swap_review.tx_failed_1": "* Please try again or ", "swap_review.tx_failed_2": "contact support", "swap_review.tx_pending": "* Leaving won’t stop the process. Check details in history.", "swap_review.tx_success": "All done! You can close this tab anytime.", "swap_review.you_pay": "You pay", "swap_review.you_receive": "You receive", "swap_settings.manage_bridge": "Manage bridge provider", "swap_settings.manage_chain_tip": "*You can enable or disable for specific chains.", "swap_settings.manage_swap": "Manage swap provider", "swap_token_selector.all_token": "All token", "swap_token_selector.contract_info": "View in explorer", "swap_token_selector.popular_token": "Popular token", "swap_token_selector.trending_token": "Trending token", "symbol_and_more": "{symbol} and more", "terms_privacy": "Use implies consent to our <termsTag>Terms</termsTag> & <privacyTag>Privacy</privacyTag>", "title_403": "403 Forbidden", "title__edit_fee": "<PERSON>", "title__invoice_description": "Invoice description", "title__lnurl_pay": "LNURL Pay", "toast.web_auth": "Set failed", "token_hidden_message": "This token is currently hidden and won't appear in the list", "token_manage.custom_token_address_failed": "Failed to find the contract address. Try again.", "token_manage.hidden_token": "{num} Hidden token", "token_no_search_results_desc": "No match found for your search. Please re-enter.", "token_selector.empty_content": "The token selected does not support cross-chain to this network.", "token_selector.empty_title": "Unsupported", "token_selector.network": "Network:", "token_selector.risk_reminder.button_cancel": "Cancel", "token_selector.risk_reminder.button_ok": "OK", "token_selector.risk_reminder.checkbox": "Don't show this alert for all non-verified tokens", "token_selector.risk_reminder.malicious_token_alert": "Caution! Malicious token.", "token_selector.risk_reminder.message": "Anyone can issue tokens, including counterfeit tokens under valid projects. User who bought counterfeit tokens might not be able to sell them, resulting in asset loss. If you proceed to trade this custom token, you’ll be liable to all potential risk and responsibilities.", "token_selector.risk_reminder.spam_token_alert": "Suspected spam token", "token_selector.risk_reminder.title": "Risk reminder", "token_selector.risk_reminder.token_address": "{token_address}", "token_selector.risk_reminder.token_name": "{token_name}", "token_selector.risk_reminder.token_symbol": "{token_symbol}", "token_selector.search_placeholder": "Search symbol or contract address", "token_selector.title": "Select token", "token_selector.unverified_token_warning": "Unverified Token below. Proceed with caution.", "touch_id_unlock_desc": "<PERSON><PERSON><PERSON> is trying to <PERSON>lock.", "transaction.advanced": "Advanced", "transaction.application": "dApp", "transaction.cancel": "Cancel", "transaction.confirm": "Confirm", "transaction.current_gwei": "Current: {amount} Gwei", "transaction.custom": "Custom", "transaction.data": "Data", "transaction.estimate_gas_limit": "Estimate gas limit is {amount}, recommend {amount}", "transaction.expected_fee": "Expected Fee", "transaction.fast": "Fast", "transaction.fee_estimate": "Fee Estimate", "transaction.layer_base_fee": "L1 Base Fee", "transaction.max_base_fee": "Max base fee", "transaction.max_fee": "<PERSON>", "transaction.network": "Network", "transaction.normal": "Normal", "transaction.slow": "Slow", "transaction.to_contract": "To contract", "transaction__contract_interaction": "Contract interaction", "transaction__transaction_confirm": "Transaction confirm", "transaction_confirm.batch_swap_tip": "By submitting this order, you are approving {token} for trading confirming a swap on {chain} powered by {provider} API. If you have any concerns about the order, please contact us.", "transaction_confirm.single_swap_tip": "By submitting this order, you are confirming a swap on {chain} powered by {provider} API.  If you have any concerns about the order, please contact us.", "transfer.cancel_error": "Data Import Cancelled", "transfer.cancel_error_desc": "Data import from the device {deviceType} was canceled", "transfer.connect": "Connect", "transfer.connection_error": "Connection Failed", "transfer.connection_error_desc": "Connection interrupted. Please check your network.", "transfer.connection_status_connected": "Connected", "transfer.connection_status_connected_failed": "Connection failed", "transfer.connection_status_not_connected": "Not connected", "transfer.current_device": "Current", "transfer.data_exist_confirm_desc": "The following wallet already exist, do you want to overwrite?", "transfer.data_exist_confirm_keep": "Keep origin", "transfer.data_exist_confirm_overwrite": "Overwrite", "transfer.data_sent_to_target": "Please continue on the other device ({deviceType}).", "transfer.enter_code": "Enter code", "transfer.enter_pair_code_desc": "Paste the pairing code from another device", "transfer.establish_connection": "Establish connection", "transfer.invalid_code": "Invalid pairing code", "transfer.no_data": "No data available", "transfer.pair_code": "Pairing code", "transfer.pair_code_enter_over_limit": "Pairing code error limit exceeded. Please start over.", "transfer.pair_code_own_error": "Unable to connect your own pairing code", "transfer.qr_code": "QR code", "transfer.qr_step1": "Open the transfer feature on both devices", "transfer.qr_step2": "Use one device to scan the other's QR code or enter its pairing code to connect", "transfer.qr_step3": "Once connected, follow the prompts to continue", "transfer.qr_stepall_desc": "This feature is for transferring recovery phrases and private keys between devices. Support for hardware wallets is coming soon; for now, please ensure your hardware wallet's recovery phrase is securely recorded.", "transfer.qr_stepall_desc2": "The entire transfer is end-to-end encrypted, fully open-source, and verifiable. <url>https://help.onekey.so/articles/11829439-onekey-transfer-security-mechanism<underline>Learn more about our security</underline></url>.", "transfer.recent_transfers": "Recent transfers", "transfer.recent_transfers_empty": "Recent recipients will show here", "transfer.security_alert_new_device_re_pair": "New device detected. Connection interrupted for security. Please re-pair your device.", "transfer.transfer": "Transfer", "transfer.transfer_data": "Transfer data", "transfer.transfer_data_completed": "Transfer completed", "transfer.transfer_data_error_retry": "Retry", "transfer.transfer_data_preview": "Preview", "transfer.transfer_device_type_desktop": "Desktop", "transfer.transfer_device_type_extension": "Extension", "transfer.transfer_device_type_mobile": "Mobile", "transfer.transfer_device_type_tablet": "Tablet", "transfer.transfer_loading": "Waiting for the transfer to complete...", "transfer.transfer_scan_tips": "Scan this QR code with the other device", "transfer.transfer_server_server_configuration": "Server configuration", "transfer.transfer_server_server_custom": "Custom server", "transfer.transfer_server_server_custom_description": "Want to know how to deploy? <url>https://github.com/OneKeyHQ/e2ee-server/tree/main/packages/transfer-server<underline>Documentation</underline></url>", "transfer.transfer_server_server_custom_placeholder": "Please enter server information", "transfer.transfer_server_server_official": "OneKey official server", "transfer.transfer_server_server_official_description": "Default configuration, stable and reliable", "transfer.transfer_server_status_connect_failed": "Connection failed. Please check your network or server configuration.", "transfer.transfer_server_status_connected": "Connected via {serverName}", "transfer.transfer_server_status_connecting": "Connecting...", "transfer.transfer_verify_passcode_desc": "Verify the app passcode for the other device, {deviceName}, to decrypt its data", "transfer.unknown_error": "Unknown error", "transfer.unknown_error_desc": "Unknown error, please try again.", "transfer.verify_passcode": "Verify passcode", "transfer.web_only_supports_watch_only_transfer": "Web only supports transfers for Watch-Only wallets.", "troubleshooting.bluetooth": "Bluetooth Troubleshooting", "troubleshooting.change_usb_port": "Change the USB port used on your computer.", "troubleshooting.check_bluetooth": "Ensure Bluetooth is enabled on your OneKey device (disable AirGap if using OneKey Pro).", "troubleshooting.check_bridge": "Check if the Bridge is installed correctly. <tag>See details here</tag>.", "troubleshooting.close_other_onekey_app": "Close any other OneKey apps, extensions, or web pages.", "troubleshooting.connect_and_unlock": "Connect and unlock your device to see if it's detected.", "troubleshooting.desktop_bluetooth_usb_priority": "If the app detects another OneKey device connected via USB, it will prioritize USB communication. Please unplug the USB and try again.", "troubleshooting.device_powered_on": "Make sure your device is powered on.", "troubleshooting.fallback_solution_label": "Still can't connect?", "troubleshooting.help_center": "Visit our <tag>Help Center</tag> where we’ve pre-entered the specific keyword to assist you in finding the information you need.", "troubleshooting.reconnect_and_pair": "Reconnect and pair your OneKey device.", "troubleshooting.remove_device_from_bluetooth_list": "Go to your system settings and remove the OneKey device from the list of paired devices.", "troubleshooting.remove_usb_dongles": "Remove any USB dongles.", "troubleshooting.replug_usb_cable": "Replug the USB cable.", "troubleshooting.request": "If you still can’t find a solution, <tag>click here</tag> to contact our support team for further assistance.", "troubleshooting.restart_app": "Restart the OneKey app.", "troubleshooting.show_helper_cta_label": "Having trouble connecting your device?", "troubleshooting.solution_x": "Solution {number}", "troubleshooting.try_different_usb_cable": "Try a different USB cable, making sure it supports data transfer.", "troubleshooting.unlock_device": "Unlock your device to see if it's detected.", "troubleshooting.usb": "USB Troubleshooting", "troubleshooting.use_original_usb_cable": "Use the original USB cable if possible.", "trx_consumed": "TRX consumed", "tx_accelerate.accelerator_selector_item_label": "{name} accelerator", "tx_accelerate.order_inquiry_label": "Order inquiry", "tx_accelerate.speed_up_with_accelerator_dialog_desc": "Your transaction ID will be automatically filled in for a seamless experience.", "tx_accelerate.speed_up_with_accelerator_dialog_fee_refund": "Fees are non-refundable", "tx_accelerate.speed_up_with_accelerator_dialog_note_fee_cal": "Additional fees will be calculated by {accelerator}", "tx_accelerate.speed_up_with_accelerator_dialog_note_service_provide_by": "Service provided by {accelerator}", "tx_accelerate.speed_up_with_accelerator_dialog_note_title": "Note", "tx_accelerate.speed_up_with_accelerator_dialog_title": "Speed up with {accelerator}", "update.all_other_apps_closed": "All other OneKey Apps and web upgrade tools are closed.", "update.all_other_apps_closed_emoji": "All other OneKey Apps and web upgrade tools are closed. 🆗", "update.all_updates_complete": "All updates complete 👏🏻", "update.app_up_to_date": "You’ve got the latest version of <PERSON><PERSON><PERSON>, thanks for staying on the ball.", "update.app_update": "App update", "update.app_update_latest_version": "You’re all good", "update.app_version_ready_for_update": "App {version} update ready", "update.bluetooth_version_available": "Bluetooth {version} is available", "update.bridge_network_error": "Bridge network error", "update.bridge_not_installed": "Bridge not installed", "update.bridge_timeout_error": "Bridge timeout error", "update.changelog_title": "Update to v{ver} available", "update.changelog_updated_title": "Welcome to v{ver}", "update.check_connection_try_again": "Check your device's connection and try again.", "update.checking_device": "Checking device...", "update.checking_device_if_no_restart": "Checking device... If it doesn't restart automatically after installation, manually restart to continue.", "update.checking_for_updates": "Checking for updates...", "update.checking_latest_ui_resources": "Checking latest UI resources...", "update.connection_interrupted": "Connection interrupted", "update.connection_interrupted_desc": "The connection has been lost. Please check your network settings and try again.", "update.connection_to_bridge_timed_out": "The connection to the bridge has timed out. Please check your internet connection and try again.", "update.device_connected_via_bluetooth": "My device is connected via Bluetooth and the connection is stable.", "update.device_connected_via_bluetooth_emoji": "My device is connected via Bluetooth and the connection is stable. 📲", "update.device_connected_via_usb": "My device is connected via USB cable.", "update.device_connected_via_usb_emoji": "My device is connected via USB cable. 🔌", "update.device_disconnected": "<PERSON><PERSON> disconnected", "update.device_disconnected_desc": "The device has been disconnected. Please reconnect the device and try again.", "update.device_fully_charged": "The device battery is fully charged and connected to power.", "update.device_fully_charged_emoji": "The device battery is fully charged and connected to power. 🔋", "update.device_in_bootloader_mode": "Device in bootloader mode", "update.device_mismatch_detected": "Device mismatch detected", "update.device_mismatch_detected_desc": "A different device has been detected. Please connect the original device intended for this operation.", "update.download_and_verify_text": "Download and verify", "update.download_asc_desc": "Retrieve ASC signature for package verification.", "update.download_asc_label": "Download signature file", "update.download_failed": "Download failed", "update.download_on_github": "Download on Github", "update.download_package_desc": "{file} is being downloaded. View release details on GitHub Releases: <url><underline>https://github.com/OneKeyHQ/app-monorepo/releases</underline></url>", "update.download_package_from_source_text": "From <tag></tag>", "update.download_package_label": "Download package", "update.download_success": "Download success", "update.download_timed_out_check_connection": "Download timed out, please check your internet connection.", "update.downloading": "Downloading...", "update.downloading_latest_ui_resources": "Downloading latest UI resources...", "update.downloading_package": "Downloading... {progress}%", "update.ensure_one_usb_device_connected": "Please ensure only one USB device is connected to proceed with the upgrade. If multiple devices are connected, disconnect the others and try again.", "update.failed_to_enter_bootloader_mode": "Failed to enter bootloader mode", "update.firmware_version_available": "Firmware {version} is available", "update.follow_online_tutorial_to_proceed_manually": "Follow the <url>online tutorial</url> to proceed manually, then click \"Retry\".", "update.hardware_sdk_initialization_failed": "Hardware SDK initialization failed. Please check your network or switch the proxy and try again.", "update.hardware_update": "Hardware update", "update.hardware_update_requires_bridge": "Hardware update requires the latest bridge software. Please visit our online tutorial <url>[Download and update OneKey Bridge]</url> for detailed installation instructions.", "update.hardware_wallet_in_bootloader_mode": "Your hardware wallet is in bootloader mode, which is used for software updates. Would you like to update now?\nIf you prefer not to update, please manually restart the device to return to normal mode.", "update.hardware_wallet_in_bootloader_mode_restart": "Your hardware wallet is currently in boot mode, which is used for hardware updates.\nDetected existing firmware on the device. Please restart the device to normal mode before connecting to the app.", "update.i_have_backed_up_my_recovery_phrase": "I’ve backed up my recovery phrase.", "update.i_have_backed_up_my_recovery_phrase_emoji": "I’ve backed up my recovery phrase. ✅", "update.init_iframe_load_fail": "Init iframe load fail", "update.install_and_restart": "Install and restart", "update.install_now": "Install now", "update.installation_failed": "Installation failed", "update.installation_not_safe_alert_text": "Package integrity check failed - Installation not safe", "update.installation_package_possibly_compromised": "Verification incomplete - Technical issue encountered", "update.installing": "Installing...", "update.insufficient_battery_power": "Insufficient battery power", "update.insufficient_battery_power_desc": "The connected hardware device has insufficient battery power and cannot proceed with the upgrade. Please charge the device before trying again.", "update.insufficient_disk_space_clear_retry": "Insufficient disk space, please clear and retry.", "update.keep_bluetooth_connected_and_app_active": "Keep Bluetooth connected and app active", "update.keep_usb_connected_and_app_active": "Keep USB connected and app active", "update.latest_version": "You are on the latest version", "update.manual_update": "Manual install", "update.manually_entering_bootloader_mode": "Manually entering bootloader mode", "update.manually_entering_bootloader_mode_desc": "To enter bootloader mode on your OneKey Mini, press and hold the lock screen button while inserting the data cable into the computer, then click 'Verify status and continue'.", "update.network_exception_check_connection": "Network exception, please check your internet connection.", "update.network_instability_check_connection": "Check internet connection", "update.new_app_version": "New App version 🎉", "update.new_hardware_updates": "New hardware updates 🎉", "update.new_update_downloaded": "A new update has been downloaded. Would you like to install and restart the app now?", "update.only_one_device_connected": "Only one device is connected.", "update.only_one_device_connected_emoji": "Only one device is connected. 📱", "update.only_one_usb_device_supported_for_upgrade": "Only One USB Device Supported for Upgrade", "update.operation_canceled": "Operation canceled", "update.operation_canceled_desc": "The operation has been canceled. Please try again.", "update.outdated_version_detected": "Outdated version detected", "update.outdated_version_detected_desc": "Your current firmware version is too low. Please visit our online tutorial <url>[Solution for failed firmware upgrade on Touch]</url> and follow the step-by-step instructions to complete the update.", "update.package_name_mismatch": "Installation package name mismatch", "update.progress_downloading": "{progress}% downloading...", "update.quit_update": "Quit update", "update.quit_update_desc": "The firmware is being updated. Exiting may interrupt the upgrade. Are you sure you want to cancel the update?", "update.ready_to_upgrade_checklist": "Ready to upgrade? let’s check you're all set 📝", "update.reboot_success": "Reboot success", "update.reboot_to_bootloader_mode": "Reboot to bootloader mode", "update.recommend_regular_check_and_update_plugin": "To ensure you get the best experience, we recommend that you regularly check for and manually update the plugin.", "update.restart_to_update": "Restart to update", "update.retrying_fails_help_text": "{reason}. If retrying fails, download the installation package from <url>https://github.com/OneKeyHQ/app-monorepo/releases<underline>OneKey’s official GitHub releases page</underline></url>—it will automatically replace the current version.", "update.server_not_responding_try_later": "Server error", "update.signature_verification_failed_alert_text": "Signature verification failed - Potential security risk", "update.transferring_data": "Transferring data...", "update.troubleshoot_connection_issues": "To troubleshoot connection issues:\n\n1. Ensure OneKey Bridge is installed and running.\n2. Refresh or switch your browser, then try again.\n3. Use a different cable and port.\n\nIf this doesn’t help, contact <url>OneKey support</url>.", "update.troubleshoot_connection_issues_desktop": "To troubleshoot connection issues, follow these steps:\n\n1. Reconnect your device and try again.\n2. Restart the app and your device, then try again.\n3. Use a different cable and port, then try again.\n\nIf this doesn’t help, contact <url>OneKey support</url>.", "update.troubleshoot_connection_issues_mobile": "To troubleshoot connection issues:\n\n1. Ensure Bluetooth is enabled.\n2. Reconnect your device and try again.\n3. Restart the App and your device, then try again.\n\nIf this doesn’t help, contact <url>OneKey support</url>.", "update.unable_to_connect_to_bridge": "Unable to connect to the bridge. Please check your internet connection and try again.", "update.update_app_available": "App {version} update available", "update.update_completed": "Update Completed", "update.update_history": "Update history", "update.update_in_official_web_tool": "Update in official web tool", "update.update_in_official_web_tool_desc": "Your hardware wallet firmware requires an update.\nPlease visit <url>firmware.onekey.so</url> on your computer to proceed with the upgrade.", "update.update_in_official_web_tool_desc_copy": "Your hardware wallet firmware requires an update.\nPlease visit firmware.onekey.so on your computer to proceed with the upgrade.", "update.update_incomplete_cta_text": "Manual install", "update.update_incomplete_desc": "Your previous installation may have been blocked for unknown reasons. Click 'Manual Install' to open the package, then drag the app to the Applications folder to complete the update.", "update.update_incomplete_desc_linux": "Your previous installation may have been blocked for unknown reasons. Click 'Manual Install' to open the file explorer, then install the .AppImage file manually.", "update.update_incomplete_desc_windows": "Your previous installation may have been blocked for unknown reasons. Click 'Manual Install' to open the file explorer, then double-click the .exe file to install.", "update.update_incomplete_footnote": "*If your downloaded file is a .zip, double-click to unzip it first, then drag the app into the Applications folder in your Finder sidebar.", "update.update_incomplete_package_missing_desc": "The system has detected that your previous client update was not completed. You can retry the update now or do it later.", "update.update_incomplete_text": "Update incomplete", "update.update_incomplete_title": "Manual installation guide", "update.update_now": "Update now", "update.update_required": "Update required", "update.update_required_desc": "Your hardware wallet's version is outdated and must be updated to continue.", "update.update_resource_failed": "Update resource failed", "update.update_ui_resources_success": "Update UI resources success", "update.update_webview": "Your WebView version is outdated. Some features may not work properly.", "update.updated_to_latest_version": "Updated to the latest version {version}", "update.updating_bootloader": "Updating bootloader", "update.updating_ui_resources": "Updating UI resources...", "update.verify_asc_desc": "Ensure the signature is officially from OneKey.", "update.verify_asc_label": "Verify signature", "update.verify_asc_success_desc": "Official OneKey signature confirmed", "update.verify_file_signature": "Verify file signature...", "update.verify_package_desc": "Confirm the integrity of the package using SHA256 checksum.", "update.verify_package_label": "Verify package", "update.verify_package_success_desc": "Package integrity confirmed - Safe to install", "update.verify_status_and_continue": "Verify status and continue", "update.verifying": "Verifying...", "update.verifying_sha256_and_package_name": "Verifying SHA256 and package name…", "update.whats_new_in_onekey_version": "What’s new\nin OneKey {version} 👋🏻", "update_notification_dialog.desc": "This update includes new improvements to enhance your experience. Update your OneKey App now for the best experience!", "update_notification_dialog.title": "<PERSON><PERSON><PERSON> just got better!", "upgrade.recommend_usb": "Firmware upgrades are only supported via USB for better speed and stability. Please connect your device using a USB cable to continue.", "upgrade.switch_to_usb": "Switch to USB", "upgrade.use_usb": "USB required for upgrade", "v4.select_account_name_label": "Name from OneKey v4", "v4_migration.backed_up_warning": "Have you backed up everything?", "v4_migration.backed_up_warning_desc": "Ensure you’ve backed up all your recovery phrases and private keys to prevent any loss of assets due to unexpected issues during data migration.", "v4_migration.backup_alert_desc": "Before you proceed, make sure you've backed up the all recovery phrases or private keys.", "v4_migration.backup_alert_title": "IMPORTANT: Back up all of your data", "v4_migration.backup_primary_action": "I backed them up", "v4_migration.backup_private_key_reveal_alert": "Click to view the private key, make sure no one is looking your screen", "v4_migration.backup_recovery_phrase_reveal_alert": "Click to view the recovery phrase, make sure no one is looking your screen", "v4_migration.backup_title": "Backup your data", "v4_migration.completed_desc": "Explore the exciting new features of OneKey v5 now!", "v4_migration.completed_title": "Migration completed", "v4_migration.confirm_password_description": "Please enter the unlock password for OneKey App v4", "v4_migration.exit_migration_checkbox_label": "Don't ask me to migrate when the App starts", "v4_migration.update_in_progress": "Update in progress", "v4_migration.update_in_progress_alert_description": "Migration may take longer with more wallets. Please be patient.", "v4_migration.update_in_progress_alert_title": "Don't close your App during update", "v4_migration.welcome_message": "OneKey 5.0 is here!", "v4_migration.welcome_message_desc": "Here’s how to securely and quickly migrate your data. Ready to start?", "wallet.approval_approval_details": "Approval details", "wallet.approval_approved_token": "Approved token", "wallet.approval_bulk_revoke": "Bulk Revoke", "wallet.approval_bulk_revoke_alert": "Please keep the page active. Exiting will pause the process.", "wallet.approval_bulk_revoke_approved_spender": "Approved spender", "wallet.approval_bulk_revoke_one_by_one": "One by one", "wallet.approval_bulk_revoke_prime_description": "OneKey Prime members can revoke all {number} approvals with a single click.", "wallet.approval_bulk_revoke_status_paused": "Paused", "wallet.approval_bulk_revoke_status_paused_reason_excessive_gas": "Skipped bulk revoke due to excessive gas cost. Please confirm if individual revocation is still needed.", "wallet.approval_bulk_revoke_status_skipped": "Skipped", "wallet.approval_bulk_revoke_status_succeeded": "Succeeded", "wallet.approval_bulk_revoke_total_gas": "Total gas", "wallet.approval_contract_controlled_by_eoa": "Contract controlled by EOA", "wallet.approval_contract_scam_contract": "This is a known scam contract", "wallet.approval_inactive_suggestion_description": "Revoke them to free up unnecessary permissions.", "wallet.approval_inactive_suggestion_title": "{number} contracts have been inactive for over 90 days.", "wallet.approval_number": "{number} approvals", "wallet.approval_risky_detected_suggestion_description": "Revoke them to avoid potential asset loss.", "wallet.approval_risky_suggestion_title": "{number} risky approvals detected", "wallet.backup_prompt": "Backup your wallet", "wallet.buy_crypto_instruction": "Buy crypto to fund your wallet", "wallet.collapsed_assets": "Collapsed assets", "wallet.collapsed_risk_assets": "Collapsed risk assets", "wallet.collapsed_risk_assets_number": "{number} Collapsed risk assets", "wallet.connect_wallet_more_options": "Show more wallets", "wallet.currently_supports_up_to_str_all_networks_accounts": "Currently supports up to {0} accounts across all networks.", "wallet.description_no_approvals": "Your approvals will appear here", "wallet.destination_tag": "Destination tag", "wallet.disable_energy_rental_description": "Energy rental helps lower your TRON transaction fees. It’s safe, transparent, and works automatically. Turning it off will make your fees higher.", "wallet.disable_energy_rental_title": "Turn off energy rental", "wallet.energy_confirmations_required": "Sending a transaction with energy rental requires two confirmations on your hardware wallet. The first transaction rents energy, and the second sends your transfer. Please complete both promptly to avoid transaction expiration.", "wallet.energy_rental_description": "Energy rental was enabled to reduce fees due to low energy. This will change the transaction nonce. Disable it if not needed. Supports using USDT to pay gas when TRX is insufficient. The rental service is provided by a third party.", "wallet.energy_rental_insufficient_trx": "Insufficient TRX detected. USDT will be used to rent energy.", "wallet.energy_rental_low_energy_detected": "Low energy detected. Energy rental was enabled to reduce fees.", "wallet.energy_rental_title": "Energy rental", "wallet.engine_account_already_exists": "Account already exists.", "wallet.engine_account_name_length_error": "Account name length exceeds the limit.", "wallet.engine_too_many_external_accounts": "Number of external wallet accounts exceeds the limit.", "wallet.engine_too_many_hd_wallets": "Number of wallets exceeds the limit.", "wallet.engine_too_many_hw_passphrase_wallets": "Number of passphrase wallets in the hardware wallet exceeds the limit.", "wallet.engine_too_many_hw_wallets": "Number of hardware wallets exceeds the limit.", "wallet.engine_too_many_imported_accounts": "Number of single-chain accounts exceeds the limit.", "wallet.engine_too_many_watching_accounts": "Number of watch-only wallet accounts exceeds the limit.", "wallet.engine_wallet_name_length_error": "Wallet name length exceeds the limit.", "wallet.enter_redemption_code": "Enter redemption code", "wallet.error_trade_with_watched_account": "Cannot trade with a watch-only wallet.", "wallet.exchange_rate": "Exchange rate: {price_trx} TRX → {price_usdt} USDT", "wallet.exchange_usdt_description": "If you don’t have enough TRX, you can choose to use USDT to rent energy. USDT payments cost more than TRX. You may opt to receive part of your payment as TRX for future use.", "wallet.exchange_usdt_for_trx": "Exchange {price_usdt} USDT for {price_trx} TRX", "wallet.generic_string_length_requirement": "Account name length exceeds the limit.", "wallet.get_trx_for_future_fees": "Get some TRX for future fees?", "wallet.hardware_wallet_connect_description_1": "Connect with USB or QR code", "wallet.hardware_wallet_connect_description_2": "Authorize on the device", "wallet.history_footer_view_full_history_in_explorer": "Only recent transactions are shown. To view the full history, please visit the block explorer.", "wallet.history_settings_hide_risk_transaction_desc": "Supports major blockchains, including ETH, BNB, TRX, and more.", "wallet.history_settings_hide_risk_transaction_desc_unsupported": "{networkName} network is not supported yet.", "wallet.history_settings_hide_risk_transaction_title": "Hide risky transactions", "wallet.history_settings_hide_small_transaction_desc": "Filters received transactions by value. Unpriced tokens not filtered.", "wallet.history_settings_hide_small_transaction_title": "Hide small transactions (≤ $0.01)", "wallet.label_private_key": "Private key", "wallet.label_watch_only": "Watch-Only", "wallet.last_ledger_sequence": "Last ledger sequence", "wallet.ledger_index": "Ledger index", "wallet.no_address": "No address", "wallet.no_address_desc": "Create address to enable network", "wallet.no_tron_account": "No TRON account found. Create or import to continue.", "wallet.onekey_wallet_without_description": "Go to Chrome Web Store", "wallet.onekey_wallet_without_refresh": "Plugin installed? Please refresh the page and try again", "wallet.pay_with_usdt": "Pay with USDT", "wallet.receive_token_instruction": "Deposit from other wallets", "wallet.redeem_label": "Redeem", "wallet.revoke_suggestion": "Revoke suggestion", "wallet.risk_assets": "Risk assets", "wallet.risk_assets_always_visible_on_home": "Always visible on home", "wallet.risk_assets_description": "This category includes assets our system has flagged as spam or malicious.", "wallet.risk_assets_hide_on_home": "<PERSON><PERSON> on home", "wallet.risk_assets_hide_on_home_feedback_hidden": "{tokenSymbol} is now hidden from home", "wallet.risk_assets_hide_on_home_feedback_shown": "{tokenSymbol} is now shown on home", "wallet.risk_assets_show_on_home": "Show on home", "wallet.save_amount": "Save {number} TRX", "wallet.subsidy_all_used": "All used", "wallet.subsidy_claim": "<PERSON><PERSON><PERSON>", "wallet.subsidy_claimed": "Claimed", "wallet.subsidy_description": "Resource is valid for 10 minutes. Complete your transaction within the time limit. Claim once every 24h (GMT+8 reset), max 10 times per month.", "wallet.subsidy_label": "Free resource", "wallet.subsidy_redeem_title": "Claim free resource", "wallet.subsidy_remaining": "Remaining {remaining} / {total}", "wallet.title_no_approvals": "No approvals", "wallet.track_any_address_placeholder": "Enter address", "wallet.trx_free_credit": "Free Claim", "wallet.unsupported_network_desc": "Switch networks in the top left or use a supported account", "wallet.unsupported_network_desc_alt": "Switch networks on the previous page or use a supported account", "wallet.unsupported_network_title": "This account doesn’t support {network}", "wallet.wallet_device_has_been_reset_alert_desc": "This device has created new wallets and cannot communicate with the original wallet.", "wallet.wallet_device_has_been_reset_alert_title": "Wallet device has been reset", "wallet.you_need_str_accounts_on_any_network_to_create": "You need at least {0} accounts on any single network to create.", "wallet_backup.backup_confirmation": "I've backed up", "wallet_backup.backup_reminder": "Make sure to backup your recovery phrase before viewing your address or making any deposits", "wallet_backup.backup_warning": "<strong>Backup your wallet</strong> — protect your assets from device loss", "wallet_backup.save_recovery_phrase": "Save recovery phrase", "wallet_backup.status_not_backed_up": "Not backed up", "word": "Word"}
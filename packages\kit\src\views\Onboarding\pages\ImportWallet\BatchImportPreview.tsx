import { useCallback, useState } from 'react';
import { useIntl } from 'react-intl';

import {
  <PERSON>ert,
  Badge,
  Button,
  Dialog,
  Form,
  Input,
  ListView,
  Page,
  SizableText,
  Stack,
  useForm,
} from '@onekeyhq/components';
import { ListItem } from '@onekeyhq/kit/src/components/ListItem';
import useAppNavigation from '@onekeyhq/kit/src/hooks/useAppNavigation';
import { ETranslations } from '@onekeyhq/shared/src/locale';
import { EOnboardingPages } from '@onekeyhq/shared/src/routes';
import type { IOnboardingParamList } from '@onekeyhq/shared/src/routes';

type IRouteParams = IOnboardingParamList[EOnboardingPages.BatchImportPreview];

type IWalletItem = {
  index: number;
  input: string;
  isValid: boolean;
  error?: string;
  name?: string;
};

type IEditFormValues = {
  name: string;
};

export function BatchImportPreview() {
  const intl = useIntl();
  const navigation = useAppNavigation();
  const [isProcessing, setIsProcessing] = useState(false);

  // 获取路由参数
  const routeParams = navigation.getParams<IRouteParams>();
  const { importType, inputData, parsedData: initialParsedData } = routeParams;

  // 本地状态管理钱包列表
  const [walletList, setWalletList] = useState<IWalletItem[]>(
    initialParsedData.map((item, index) => ({
      ...item,
      name: item.name || `${importType === 'privateKey' ? 'Wallet' : 'Wallet'} ${index + 1}`,
    }))
  );

  // 统计信息
  const validCount = walletList.filter(item => item.isValid).length;
  const invalidCount = walletList.filter(item => !item.isValid).length;
  const totalCount = walletList.length;

  // 删除钱包
  const handleDeleteWallet = useCallback((index: number) => {
    setWalletList(prev => prev.filter((_, i) => i !== index));
  }, []);

  // 编辑钱包名称
  const handleEditWalletName = useCallback((index: number, currentName: string) => {
    const form = useForm<IEditFormValues>({
      defaultValues: { name: currentName },
    });

    const dialog = Dialog.show({
      title: intl.formatMessage({ id: ETranslations.global_edit_wallet_name }),
      renderContent: (
        <Form form={form}>
          <Form.Field
            name="name"
            rules={{
              required: intl.formatMessage({ id: ETranslations.form_required }),
              maxLength: {
                value: 50,
                message: intl.formatMessage(
                  { id: ETranslations.form_max_length },
                  { length: 50 }
                ),
              },
            }}
          >
            <Input
              placeholder={intl.formatMessage({ id: ETranslations.global_wallet_name })}
              autoFocus
            />
          </Form.Field>
        </Form>
      ),
      onConfirm: async () => {
        const values = form.getValues();
        if (values.name.trim()) {
          setWalletList(prev => 
            prev.map((item, i) => 
              i === index ? { ...item, name: values.name.trim() } : item
            )
          );
        }
        await dialog.close();
      },
      onCancel: async () => {
        await dialog.close();
      },
    });
  }, [intl]);

  // 开始导入
  const handleStartImport = useCallback(async () => {
    const validWallets = walletList.filter(item => item.isValid);
    if (validWallets.length === 0) {
      return;
    }

    setIsProcessing(true);
    try {
      // 导航到结果页面
      navigation.push(EOnboardingPages.BatchImportResult, {
        importType,
        totalCount: validWallets.length,
        successCount: 0,
        failedCount: 0,
        results: validWallets.map(item => ({
          index: item.index,
          input: item.input,
          success: false,
          walletId: undefined,
          accountId: undefined,
        })),
      });
    } finally {
      setIsProcessing(false);
    }
  }, [navigation, importType, walletList]);

  // 渲染钱包列表项
  const renderWalletItem = useCallback(({ item, index }: { item: IWalletItem; index: number }) => {
    const displayInput = item.input.length > 20 
      ? `${item.input.slice(0, 20)}...` 
      : item.input;

    return (
      <ListItem
        key={`wallet-${index}`}
        title={item.name}
        subtitle={displayInput}
        subtitleProps={{
          color: '$textSubdued',
          fontFamily: '$mono',
        }}
      >
        <ListItem.Avatar>
          <Badge
            size="sm"
            variant={item.isValid ? 'success' : 'destructive'}
          >
            {item.isValid ? '✓' : '✗'}
          </Badge>
        </ListItem.Avatar>
        <ListItem.Text
          primary={item.name}
          secondary={item.isValid ? displayInput : item.error}
          secondaryTextProps={{
            color: item.isValid ? '$textSubdued' : '$textCritical',
            fontFamily: '$mono',
            numberOfLines: 1,
          }}
        />
        <Stack direction="row" gap="$2">
          {item.isValid && (
            <ListItem.IconButton
              icon="PencilOutline"
              title={intl.formatMessage({ id: ETranslations.global_edit })}
              onPress={() => handleEditWalletName(index, item.name || '')}
            />
          )}
          <ListItem.IconButton
            icon="TrashOutline"
            iconProps={{ color: '$iconCritical' }}
            title={intl.formatMessage({ id: ETranslations.global_delete })}
            onPress={() => handleDeleteWallet(index)}
          />
        </Stack>
      </ListItem>
    );
  }, [intl, handleEditWalletName, handleDeleteWallet]);

  return (
    <Page scrollEnabled>
      <Page.Header
        title={intl.formatMessage({
          id: ETranslations.global_batch_import_preview,
        })}
      />
      <Page.Body px="$5">
        <Stack gap="$4">
          {/* 统计信息 */}
          <Stack
            direction="row"
            gap="$2"
            alignItems="center"
            justifyContent="space-between"
            p="$3"
            bg="$bgSubdued"
            borderRadius="$3"
          >
            <SizableText size="$bodyMd" color="$textSubdued">
              {intl.formatMessage({ id: ETranslations.global_preview_summary })}
            </SizableText>
            <Stack direction="row" gap="$2" alignItems="center">
              <Badge size="sm" variant="success">
                {intl.formatMessage(
                  { id: ETranslations.global_valid_count },
                  { count: validCount }
                )}
              </Badge>
              {invalidCount > 0 && (
                <Badge size="sm" variant="destructive">
                  {intl.formatMessage(
                    { id: ETranslations.global_invalid_count },
                    { count: invalidCount }
                  )}
                </Badge>
              )}
              <Badge size="sm" variant="neutral">
                {intl.formatMessage(
                  { id: ETranslations.global_total_count },
                  { count: totalCount }
                )}
              </Badge>
            </Stack>
          </Stack>

          {/* 提示信息 */}
          {invalidCount > 0 && (
            <Alert
              type="warning"
              title={intl.formatMessage({
                id: ETranslations.global_batch_import_invalid_warning,
              })}
              description={intl.formatMessage({
                id: ETranslations.global_batch_import_invalid_warning_description,
              })}
            />
          )}

          {/* 钱包列表 */}
          <Stack flex={1}>
            <SizableText size="$headingSm" mb="$3">
              {intl.formatMessage({ id: ETranslations.global_wallet_list })}
            </SizableText>
            <ListView
              data={walletList}
              renderItem={renderWalletItem}
              keyExtractor={(item, index) => `wallet-${index}`}
              estimatedItemSize={80}
              showsVerticalScrollIndicator={false}
            />
          </Stack>
        </Stack>
      </Page.Body>
      <Page.Footer
        confirmButtonProps={{
          disabled: validCount === 0 || isProcessing,
          loading: isProcessing,
        }}
        onConfirm={handleStartImport}
        confirmButtonText={intl.formatMessage(
          { id: ETranslations.global_import_count_wallets },
          { count: validCount }
        )}
      />
    </Page>
  );
}

export default BatchImportPreview;

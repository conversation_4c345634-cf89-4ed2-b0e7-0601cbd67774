name: 01-lint

on:
  pull_request:
    branches:
      - onekey
      - x
  push:
    branches:
      - onekey
      - x

# Cancel a currently running workflow from the same PR/branch/tag
# when a new workflow is triggered
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  lint:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [20.x]

    steps:
      - uses: actions/checkout@v3
        with:
          lfs: true

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: 'https://npm.pkg.github.com'
          always-auth: true
          scope: '@onekeyhq'

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn config get cacheFolder)" >> $GITHUB_OUTPUT

      - name: Cache dependencies
        uses: actions/cache@v3
        id: yarn-cache
        with:
          path: |
            ${{ steps.yarn-cache-dir-path.outputs.dir }}
            **/node_modules
          key: lint-${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock', '**/patches/**') }}
          restore-keys: |
            lint-${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock', '**/patches/**') }}

      - name: Install Dependency
        if: steps.yarn-cache.outputs.cache-hit != 'true'
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_OPTIONS: '--max_old_space_size=4096'
        run: |
          yarn --mode=skip-build && yarn patch-package

      - name: Run postinstall only (cache hit)
        if: steps.yarn-cache.outputs.cache-hit == 'true'
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_OPTIONS: '--max_old_space_size=4096'
        run: |
          yarn postinstall
      
      - name: Run lint
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_OPTIONS: '--max_old_space_size=4096'
        run: |
          yarn lint

import { useIntl } from 'react-intl';

import type { IIconProps, IPropsWithTestId } from '@onekeyhq/components';
import {
  Icon,
  Page,
  SectionList,
  SizableText,
  Stack,
} from '@onekeyhq/components';
import backgroundApiProxy from '@onekeyhq/kit/src/background/instance/backgroundApiProxy';
import type { IListItemProps } from '@onekeyhq/kit/src/components/ListItem';
import { ListItem } from '@onekeyhq/kit/src/components/ListItem';
import useAppNavigation from '@onekeyhq/kit/src/hooks/useAppNavigation';
import { useUserWalletProfile } from '@onekeyhq/kit/src/hooks/useUserWalletProfile';
import { ETranslations } from '@onekeyhq/shared/src/locale';
import { defaultLogger } from '@onekeyhq/shared/src/logger/logger';
import { EOnboardingPages } from '@onekeyhq/shared/src/routes';

type IOptionItem = IPropsWithTestId<{
  title?: string;
  description?: string;
  icon: IIconProps['name'];
  iconColor?: IIconProps['color'];
  badge?: React.ReactNode;
  onPress?: IListItemProps['onPress'];
  isLoading?: boolean;
  comingSoon?: boolean;
}>;

type IOptionSection = {
  sectionTitle?: string;
  data: IOptionItem[];
};

export function BatchImportWalletOptions() {
  const intl = useIntl();
  const navigation = useAppNavigation();
  const { isSoftwareWalletOnlyUser } = useUserWalletProfile();

  const handleBatchImportPrivateKeyPress = async () => {
    await backgroundApiProxy.servicePassword.promptPasswordVerify();
    navigation.push(EOnboardingPages.BatchImportInput, {
      importType: 'privateKey',
    });
    defaultLogger.account.wallet.addWalletStarted({
      addMethod: 'BatchImportWallet',
      details: {
        importType: 'privateKey',
      },
      isSoftwareWalletOnlyUser,
    });
  };

  const handleBatchImportMnemonicPress = async () => {
    await backgroundApiProxy.servicePassword.promptPasswordVerify();
    navigation.push(EOnboardingPages.BatchImportInput, {
      importType: 'mnemonic',
    });
    defaultLogger.account.wallet.addWalletStarted({
      addMethod: 'BatchImportWallet',
      details: {
        importType: 'mnemonic',
      },
      isSoftwareWalletOnlyUser,
    });
  };

  const options: IOptionSection[] = [
    {
      sectionTitle: intl.formatMessage({
        id: ETranslations.global_batch_import,
      }),
      data: [
        {
          title: intl.formatMessage({
            id: ETranslations.global_batch_import_private_key,
          }),
          description: intl.formatMessage({
            id: ETranslations.global_batch_import_private_key_description,
          }),
          icon: 'Key2Outline',
          onPress: handleBatchImportPrivateKeyPress,
          testID: 'batch-import-private-key',
        },
        {
          title: intl.formatMessage({
            id: ETranslations.global_batch_import_mnemonic,
          }),
          description: intl.formatMessage({
            id: ETranslations.global_batch_import_mnemonic_description,
          }),
          icon: 'SecretPhraseOutline',
          onPress: handleBatchImportMnemonicPress,
          testID: 'batch-import-mnemonic',
        },
      ],
    },
  ];

  const renderSectionHeader = ({ section }: { section: IOptionSection }) => (
    <SizableText
      size="$headingSm"
      color="$textSubdued"
      px="$5"
      py="$2"
      bg="$bgApp"
    >
      {section.sectionTitle}
    </SizableText>
  );

  const renderItem = ({ item }: { item: IOptionItem }) => (
    <ListItem
      title={item.title}
      subtitle={item.description}
      onPress={item.onPress}
      testID={item.testID}
    >
      <ListItem.IconButton
        icon={item.icon}
        iconProps={{
          color: item.iconColor,
        }}
      />
      <ListItem.Text
        primary={item.title}
        secondary={item.description}
        primaryTextProps={{
          size: '$bodyLgMedium',
        }}
        secondaryTextProps={{
          size: '$bodyMd',
          color: '$textSubdued',
        }}
      />
      <ListItem.DrillIn />
    </ListItem>
  );

  return (
    <Page scrollEnabled>
      <Page.Header
        title={intl.formatMessage({
          id: ETranslations.global_batch_import_wallet,
        })}
      />
      <Page.Body>
        <Stack px="$5" py="$4">
          <SizableText size="$bodyMd" color="$textSubdued">
            {intl.formatMessage({
              id: ETranslations.global_batch_import_wallet_description,
            })}
          </SizableText>
        </Stack>
        <SectionList
          sections={options}
          renderSectionHeader={renderSectionHeader}
          renderItem={renderItem}
          keyExtractor={(item, index) => `${item.title}-${index}`}
          stickySectionHeadersEnabled={false}
          showsVerticalScrollIndicator={false}
        />
      </Page.Body>
    </Page>
  );
}

export default BatchImportWalletOptions;

import type { IFormFieldProps } from '../types';
import type { StackStyle } from '@tamagui/web';

type INonGestureStackStyleProps = Omit<
  StackStyle,
  | 'onPanStart'
  | 'onPanMove'
  | 'onPanEnd'
  | 'onHover'
  | 'hoverStyle'
  | 'pressStyle'
  | 'focusVisibleStyle'
  | 'onTouchStart'
  | 'onTouchMove'
  | 'onTouchEnd'
  | 'pointerEvents'
>;

interface IBaseGestureSliderProps extends INonGestureStackStyleProps {
  disabled?: boolean;
  min: number;
  max: number;
  step: number;
  defaultValue?: number;
  onSlideStart?: () => void;
  onSlideMove?: (value: number) => void;
  onSlideEnd?: () => void;
}

export type IBaseSliderProps = IFormFieldProps<number, IBaseGestureSliderProps>;

name: release-desktop-winms

on:
  workflow_run:
    workflows:
      - daily-build
    types:
      - completed
  workflow_dispatch:

jobs:
  release-desktop-winms:
    runs-on: macos-latest
    strategy:
      matrix:
        node-version: [20.x]

    if: ${{ !github.event.workflow_run || (github.event.workflow_run && github.event.workflow_run.conclusion == 'success') }}
    steps:
      - name: Show executed time
        run: |
          echo "Executed at: $(date '+%Y-%m-%d %H:%M:%S')"

      - name: Checkout Source Code
        uses: actions/checkout@v3
        with:
          lfs: true

      - name: Run Shared Env Setup
        uses: ./.github/actions/shared-env
        with:
          env_file_name: '.env'
          sentry_project: 'desktop-winms'
          covalent_key: ${{ secrets.COVALENT_KEY }}
          sentry_token: ${{ secrets.SENTRY_TOKEN }}
          sentry_dsn_react_native: ${{ secrets.SENTRY_DSN_REACT_NATIVE }}
          sentry_dsn_web: ${{ secrets.SENTRY_DSN_WEB }}
          sentry_dsn_desktop: ${{ secrets.SENTRY_DSN_DESKTOP }}
          sentry_dsn_mas: ${{ secrets.SENTRY_DSN_MAS }}
          sentry_dsn_snap: ${{ secrets.SENTRY_DSN_SNAP }}
          sentry_dsn_winms: ${{ secrets.SENTRY_DSN_WINMS }}
          sentry_dsn_ext: ${{ secrets.SENTRY_DSN_EXT }}

      - name: 'Setup ENV'
        run: |
          eval "$(node -e 'const v=require("./apps/desktop/package.json").version; console.log("pkg_version="+v)')"
          echo '$pkg_version='$pkg_version
          echo "PKG_VERSION=$pkg_version" >> $GITHUB_ENV

          artifacts_url="$GITHUB_SERVER_URL/$GITHUB_REPOSITORY/actions/runs/$GITHUB_RUN_ID"
          echo "ARTIFACTS_URL=$artifacts_url" >> $GITHUB_ENV


      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: 'https://npm.pkg.github.com'
          always-auth: true
          scope: '@onekeyhq'

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn config get cacheFolder)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v3
        id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Install Dep
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_OPTIONS: '--max_old_space_size=8192'
        run: |
          yarn

      - name: Setup Code Signing file
        run: |
          echo ${{ secrets.DESKTOP_KEYS_SECRET }} | base64 -d > apps/desktop/sign.p12

      - name: Publish and Sign Static Windows Installer
        if: ${{ (github.event_name == 'workflow_dispatch') && (github.ref == 'refs/heads/onekey') }}
        env:
          NODE_OPTIONS: '--max_old_space_size=8192'
          APPLEID: ${{ secrets.APPLEID }}
          APPLEIDPASS: ${{ secrets.APPLEIDPASS }}
          ASC_PROVIDER: ${{ secrets.ASC_PROVIDER }}
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CSC_KEY_PASSWORD: ${{ secrets.CSC_KEY_PASSWORD }}
          CSC_LINK: './sign.p12'
        run: 'cd apps/desktop && yarn publish:winms'

      - name: Build and Sign Static Windows Installer
        env:
          NODE_OPTIONS: '--max_old_space_size=8192'
          APPLEID: ${{ secrets.APPLEID }}
          APPLEIDPASS: ${{ secrets.APPLEIDPASS }}
          ASC_PROVIDER: ${{ secrets.ASC_PROVIDER }}
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          CSC_KEY_PASSWORD: ${{ secrets.CSC_KEY_PASSWORD }}
          CSC_LINK: './sign.p12'
        run: 'cd apps/desktop && yarn build:winms'

      - name: Upload Artifacts Windows
        uses: actions/upload-artifact@v4
        with:
          name: onekey-desktop-windows
          path: |
            ./apps/desktop/build-electron/*.exe

      - name: Upload Artifacts Release
        uses: actions/upload-artifact@v4
        with:
          name: release
          path: |
            ./apps/desktop/build-electron/*
            !./apps/desktop/build-electron/win-unpacked
            !./apps/desktop/build-electron/mac-arm64
            !./apps/desktop/build-electron/mac
            !./apps/desktop/build-electron/linux-unpacked
            !./apps/desktop/build-electron/builder-debug.yml

      - name: 'Notify to Slack'
        if: ${{ github.event.workflow_run }}
        uses: onekeyhq/actions/slack-notify-webhook@main
        with:
          web-hook-url: ${{ secrets.SLACK_NOTIFICATION_WEBHOOK }}
          secret-key: ${{ secrets.ACTION_SIGN_SECERT_KEY }}
          artifact-type: Desktop
          artifact-name: OneKey-Desktop-Win-Store
          artifact-bundle-id: 'so.onekey.wallet.desktop'
          artifact-version-name: '${{ env.BUILD_APP_VERSION }}@${{ env.BUILD_NUMBER }}'
          artifact-version-code: '${{ env.BUILD_NUMBER }}'
          artifact-download-url: '${{ env.ARTIFACTS_URL }}'

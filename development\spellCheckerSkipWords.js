/* eslint-disable spellcheck/spell-checker */
module.exports = [
  'nodata',
  'tradingview',
  'ohlcv',
  '_j_msgid',
  '020da363502074fefdfbb07ec47abc974207951dcb1aa3c910f4a768e2c70f9c68',
  '03171d7528ce1cc199f2b8ce29ad7976de0535742169a8ba8b5a6dd55df7e589d1',
  '091e696dbf120ff0bd235d10681bd2a4040ba47c5135269643b962dd4a2b2285297097962c9feb1204b2f5f4855e517df2b6f96fde14783730b19212937205bb01',
  '0a020a95220881a16498bf36d55740a8b7aebeb2315a67080112630a2d747970652e676f6f676c65617069732e636f6d2f70726f746f636f6c2e5472616e73666572436f6e747261637412320a15418d765ef87acee24ad4ff6f5e755f36c1ee5574241215419e9113cb852004f53b25d8d565b6a1c8c310fb6118a08d0670d1e5aabeb231',
  '0x1fffffffffffff',
  '0xf7ad23226db5c1c00ca0ca1468fd49c8f8bbc1489bc1c382de5adc557a69c229',
  '0xffffffffffffffffn',
  '0xxxxxxx',
  '100vw',
  '10xxxxxx',
  'perp',
  'hyperliquid',
  'PerpTrade',
  'perpTradeRouters',
  'thirdparty',
  'perps',
  'cors',
  '110xxxxx',
  '1110xxxx',
  '418d765ef87acee24ad4ff6f5e755f36c1ee557424',
  '419e9113cb852004f53b25d8d565b6a1c8c310fb61',
  'aamt',
  'Abi',
  'e2ee',
  'jsbridge',
  'Abis',
  'undef',
  'acc',
  'beforeunload',
  'nobleRipemd160',
  'bitwarden',
  'Accum',
  'ack',
  'aclose',
  'Decipheriv',
  'ada',
  'nums',
  'addr',
  'Addr',
  'addr1',
  'Ae2',
  'aes',
  'airgap',
  'akash',
  'alg',
  'algo',
  'Asmcrypto',
  'Algo',
  'Algod',
  'algodv2',
  'Algodv2',
  'Algodv2',
  'algorand',
  'alloc',
  'allowlist',
  'allowpopups',
  'alph',
  'Andkeys',
  'Anim',
  'apk',
  'apns',
  'apr',
  'aptos',
  'Aptos',
  'Apy',
  'Apys',
  'arbitrum',
  'arcv',
  'argv',
  'Arweave',
  'ascii',
  'asm',
  'asnd',
  'assignee',
  'authenticator',
  'Authenticator',
  'autofix',
  'autoplay',
  'axfer',
  'axios',
  'Axios',
  'babylon',
  'backends',
  'backgrounded',
  'backuped',
  'bassed',
  'bch',
  'Bch',
  'bcrypto',
  'bcs',
  'Bcs',
  'bech32',
  'Bech32',
  'benfen',
  'bezier',
  'bfc',
  'bfr',
  'BIish',
  'biometric',
  'Biometric',
  'biometrics',
  'bip',
  'bip0322',
  'bip32',
  'Bip32',
  'Bip322',
  'bip39',
  'Bip39',
  'bitcoind',
  'bitness',
  'bitwise',
  'blake',
  'blake2b',
  'Blake2b224',
  'Ble',
  'blockbook',
  'blockchain',
  'Blockdag',
  'Blockflow',
  'blockhash',
  'Blockie',
  'bluetooth',
  'Boardloader',
  'bool',
  'Bool',
  'Bootloader',
  'brc20',
  'bs58',
  'bs58check',
  'bsc',
  'btc',
  'Btc',
  'btg',
  'buf',
  'Buf',
  'bufs',
  'bundler',
  'bytesize',
  'bytify',
  'cacheable',
  'caips',
  'calc',
  'camelcase',
  'cancelable',
  'Cancelled',
  'cancelling',
  'captcha',
  'Captcha',
  'cardano',
  'Cardano',
  'cbc',
  'Cbc',
  'Cbor',
  'ccall',
  'cex',
  'Cex',
  'cfx',
  'Cfx',
  'changelly',
  'Changelly',
  'changelog',
  'chanid',
  'checkbox',
  'Checkbox',
  'checksum',
  'Checksum',
  'cip',
  'ciphertext',
  'ckb',
  'Cleartext',
  'closable',
  'cloudsync',
  'Cmd',
  'cmp',
  'Cmp',
  'Codec',
  'codespace',
  'coinbase',
  'coingecko',
  'coinselect',
  'cointype',
  'Collateralized',
  'Collpase',
  'colord',
  'COMMITHASH',
  'compat',
  'cond',
  'conflux',
  'consts',
  'Consts',
  'cooldown',
  'cooldowntime',
  'copyable',
  'cosmoshub',
  'cosmwasm',
  'Cosmwasm',
  'covalent',
  'Cowswap',
  'crc',
  'crc32',
  'Cron',
  'crosshair',
  'Crosshair',
  'cronosposchain',
  'csp',
  'ctime',
  'Ctx',
  'curr',
  'Cyrl',
  'Daa',
  'dai',
  'dapp',
  'dappradar',
  'dapps',
  'dayjs',
  'dbs',
  'Ddz',
  'debounce',
  'debounced',
  'Debounced',
  'dec',
  'decompile',
  'decrypt',
  'Decrypt',
  'decrypted',
  'decrypting',
  'Deduplicate',
  'deeplink',
  'deeplinking',
  'defi',
  'Defi',
  'Defs',
  'Delegator',
  'denom',
  'Denom',
  'dep',
  'der',
  'deriver',
  'Deriver',
  'derivers',
  'derivetype',
  'deserializer',
  'Deserializer',
  'destroyable',
  'devtool',
  'dex',
  'Dex',
  'dexmarket',
  'Dexs',
  'dgb',
  'distro',
  'dkey',
  'dnt',
  'Dnx',
  'dom',
  'dont',
  'dotmap',
  'dragover',
  'dynex',
  'Dynex',
  'ecc',
  'Ecc',
  'ecdsa',
  'ecl',
  'ecrecover',
  'eddsa',
  'eip',
  'Eip',
  'eip1559',
  'Eip1559',
  'eip712',
  'ejs',
  'electron',
  'emmc',
  'Encodings',
  'encryptor',
  'encryptors',
  'endian',
  'Endian',
  'endianness',
  'Endianness',
  'endregion',
  'english',
  'Enum',
  'enums',
  'eqeqeq',
  'esm',
  'eth',
  'Ethena',
  'ethereum',
  'ethereumjs',
  'ethermint',
  'ethers',
  'everstake',
  'evm',
  'Evm',
  'exe',
  'exif',
  'expirationDateMillis',
  'extensionizer',
  'Ezo',
  'f2pool',
  'facebook',
  'faq',
  'fav',
  'favicon',
  'favicons',
  'fdv',
  'fe9e81633ce99ef11814b3b836fb2ac8e841ab45fe4e7d68422c2a6b6be7f3c2',
  'feerate',
  'fet',
  'fevm',
  'Fieldset',
  'Fil',
  'filecoin',
  'filepath',
  'filetype',
  'Fillable',
  'firefox',
  'firmwares',
  'focusable',
  'Formatjs',
  'formatter',
  'frontend',
  'fs',
  'fucntion',
  'func',
  'Func',
  'Geist',
  'getr',
  'gif',
  'gitkeep',
  'glmt',
  'globals',
  'goerli',
  'google',
  'goto',
  'gt2xl',
  'gte',
  'gwei',
  'haptic',
  'haptics',
  'Haptics',
  'Hardfork',
  'hashbuf',
  'Hasher',
  'hashify',
  'hasSysUpgrade',
  'hd',
  'hdk',
  'hdkey',
  'heading2xl',
  'heading3xl',
  'heading4xl',
  'heading5xl',
  'hermes',
  'hexify',
  'hexlify',
  'Hexs',
  'hexstring',
  'Hira',
  'hmac',
  'Hmac',
  'hodl',
  'holesky',
  'homescreen',
  'horz',
  'href',
  'hrp',
  'Huawei',
  'ibc',
  'Ibc',
  'IBip39',
  'Icahost',
  'Icloud',
  'ico',
  'idb',
  'ident',
  'Idx',
  'iframe',
  'Iframe',
  'Iish',
  'impl',
  'impls',
  'inapp',
  'infos',
  'Infos',
  'inited',
  'injective',
  'Inoffscreen',
  'Inpage',
  'instance’s',
  'instanceof',
  'instatiate',
  'integrations',
  'Integrations',
  'Interpolator',
  'ints',
  'invoker',
  'ipc',
  'Ipc',
  'iphone',
  'ipld',
  'isXprvt',
  'itms',
  'izari',
  'jane',
  'jotai',
  'Jotai',
  'jpeg',
  'jpg',
  'jpush',
  'jsdoc',
  'jsonrpc',
  'jsx',
  'juno',
  'kaspa',
  'Kaspa',
  'Kdsr',
  'keccak256',
  'keyframes',
  'keyof',
  'Keypair',
  'keyring',
  'keysend',
  'keytag',
  'keytar',
  'krc20',
  'Lamports',
  'lang',
  'lasttime',
  'Latn',
  'len',
  'linux',
  'Liquidty',
  'litecoin',
  'Lnurl',
  'localhost',
  'locktime',
  'logomark',
  'Lokalise',
  'Lookups',
  'lottie',
  'lru',
  'ltc',
  'Ltc',
  'lte',
  'macos',
  'mainnet',
  'malloc',
  'mantapacific',
  'Markt',
  'Mastercard',
  'Matic',
  'Medate',
  'Memoized',
  'memoizee',
  'mempool',
  'Merkle',
  'metamask',
  'metis',
  'metrix',
  'Metrix',
  'mev',
  'Mev',
  'Millis',
  'millisatoshis',
  'mins',
  'mintable',
  'mipd',
  'mixpanel',
  'Mixpanel',
  'mmkv',
  'monero',
  'Monero',
  'monorepo',
  'moonpay',
  'Morpho',
  'Moti',
  'msg',
  'Msg',
  'msgbuf',
  'msghash',
  'msgs',
  'Msgs',
  'mtime',
  'mul',
  'mutex',
  'namespace',
  'namespaces',
  'natsort',
  'necc',
  'neo',
  'nervos',
  'nettype',
  'neurai',
  'nexa',
  'Nexa',
  'nfc',
  'nft',
  'nfts',
  'Nh64',
  'nhashtype',
  'nist',
  'nist256p1',
  'nistp256',
  'nmc',
  'nocache',
  'nonWitnessInputPrevTxids',
  'normalizer',
  'Nostor',
  'nostr',
  'notifee',
  'notificationstate',
  'notset',
  'npub',
  'npx',
  'Nullable',
  'num',
  'Num',
  'Numberish',
  'Oauth',
  'Offchain',
  'offscreen',
  'Offscreen',
  'offscreens',
  'okx',
  'Okx',
  'OKX',
  'onboard',
  'onboarding',
  'Onchain',
  'oncomplete',
  'onekey',
  'onekeyall',
  'onekeyfe',
  'onekeyhq',
  'onekeyid',
  'onopen',
  'onramper',
  'Ons',
  'onsuccess',
  'onupgradeneeded',
  'onversionchange',
  'Opcode',
  'opcodenum',
  'opcodes',
  'Opcodes',
  'ord',
  'ordQueryStatus',
  'osx',
  'Otp',
  'Outout',
  'p2pkh',
  'p2wpkh',
  'pageview',
  'passcode',
  'passpharse',
  'passphrase',
  'pathname',
  'payin',
  'payout',
  'Pbkdf2',
  'Pendle',
  'Perf',
  'persistor',
  'Persistor',
  'pino',
  'plaform',
  'plaintext',
  'playsinline',
  'plist',
  'plusplus',
  'png',
  'pnl',
  'Polkadot',
  'polymod',
  'popui',
  'popups',
  'pos',
  'Preflight',
  'preimage',
  'Pressable',
  'pri',
  'prioritys',
  'priv',
  'Priv',
  'privatekey',
  'Privatekey',
  'privkey',
  'privy',
  'Profiler',
  'Profilers',
  'promisify',
  'protobuf',
  'prv',
  'Prv',
  'Prvkey',
  'psbt',
  'Psbt',
  'Psbts',
  'ptr',
  'Ptr',
  'pubkey',
  'Pubkey',
  'pubkeyhash',
  'publickey',
  'Publickey',
  'punycode',
  'pvtkey',
  'pwkey',
  'qrcode',
  'Queueing',
  'Rabby',
  'rbf',
  'rbtc',
  'rdns',
  'Reachability',
  'readdir',
  'readed',
  'Readonly',
  'readwrite',
  'realmdb',
  'recompressed',
  'Rect',
  'redelegate',
  'Redelegate',
  'Redux',
  'regtest',
  'Relayer',
  'renderer',
  'Renderer',
  'replacer',
  'replug',
  'rerender',
  'Rerender',
  'Resending',
  'resizable',
  'resize',
  'resized',
  'Responder',
  'revalidate',
  'revealable',
  'Revealable',
  'Revenuecat',
  'rgb',
  'ripemd160',
  'rlp',
  'rnd',
  'roi',
  'rpc',
  'rrt',
  'rsa',
  'satisfier',
  'satoshi',
  'satoshis',
  'sats',
  'sbtc',
  'scalable',
  'scalarmult',
  'schemas',
  'schnorr',
  'Schnorr',
  'scripthash',
  'scriptlen',
  'scrollable',
  'sdk',
  'Sdk',
  'secp',
  'secp256k1',
  'Secp256k1',
  'secretnetwork',
  'Secretwasm',
  'Seg',
  'segwit',
  'Segwit',
  'selectable',
  'semver',
  'sendable',
  'sepolia',
  'ser',
  'ser256',
  'ser32',
  'serializable',
  'Serializable',
  'seriliazable',
  'shasta',
  'shelley',
  'shortcode',
  'shortname',
  'shtml',
  'sig',
  'Sig',
  'sighash',
  'Sighash',
  'Signin',
  'signup',
  'sigs',
  'Sigs',
  'sigtype',
  'simpledb',
  'Sizeable',
  'snd',
  'Solana',
  'Solflare',
  'Sollet',
  'sompi',
  'Sompi',
  'Sparkline',
  'splitter',
  'sr25519',
  'starcoin',
  'Starcoin',
  'stc',
  'Stc',
  'stderr',
  'stdlib',
  'stdout',
  'storages',
  'str',
  'Str',
  'Str1',
  'Str2',
  'stringified',
  'Struct',
  'styleable',
  'Subaddress',
  'subarray',
  'subheader',
  'submenu',
  'subnetwork',
  'subnetworks',
  'subtype',
  'Sudt',
  'sui',
  'Svg',
  'swft',
  'Swft',
  'swipeable',
  'swiper',
  'sym',
  'sys',
  'Systeminformation',
  'systempreferences',
  'tabbar',
  'Tabview',
  'taiko',
  'tamagui',
  'Tamagui',
  'Tarask',
  'tatom',
  'tbtc',
  'terra',
  'terra2',
  'testnet',
  'Testnet',
  'textarea',
  'textgroup',
  'Themeable',
  'thor',
  'timelocks',
  'titchannelle',
  'Titlebar',
  'tlightning',
  'Tooltip',
  'totla',
  'Tranfer',
  'trezor',
  'Trezor',
  'trx',
  'Trx',
  'Tryopen',
  'tsc',
  'Tsecp',
  'tsx',
  'ttl',
  'tvl',
  'twopow8',
  'Tx',
  'txhash',
  'txid',
  'Txid',
  'txids',
  'txn',
  'Txn',
  'txns',
  'Txo',
  'Txs',
  'txt',
  'typeof',
  'uatom',
  'ufixed',
  'ufixedM',
  'uid',
  'Uids',
  'uint',
  'Uint16',
  'uint256',
  'Uint32',
  'Uint5',
  'uint8',
  'Uint8',
  'uints',
  'umod',
  'unbacked',
  'unbonding',
  'uncompress',
  'undelegate',
  'Undelegate',
  'unencoded',
  'Unencoded',
  'unicode',
  'uniswap',
  'unittest',
  'unlink',
  'unmaximize',
  'unmount',
  'Unprefixed',
  'unregister',
  'Unspaced',
  'unstake',
  'Unstaking',
  'unstyled',
  'uosmo',
  'upsert',
  'uptime',
  'uri',
  'urls',
  'Urls',
  'usb',
  'usd',
  'usdf',
  'usdt',
  'useragent',
  'utf',
  'utxo',
  'Utxo',
  'utxos',
  'Utxos',
  'varint',
  'Varint',
  'Varuint',
  'Ver',
  'verifier',
  'Verifier',
  'Versioned',
  'versionnum',
  'vert',
  'vespaiach',
  'viewability',
  'Viewable',
  'vitalik',
  'vout',
  'Vrku',
  'vsize',
  'vtc',
  'wagmi',
  'Wagmi',
  'walletconnect',
  'wasm',
  'Wasm',
  'wasmd',
  'watchlist',
  'webembed',
  'Webembed',
  'webembeds',
  'webextensions',
  'Webkit',
  'webln',
  'Webpack',
  'webview',
  'Webview',
  'What’s',
  'whitepaper',
  'wif',
  'wildcard',
  'withdrawable',
  'withdrawer',
  'wmic',
  'wmin',
  'wns',
  'won’t',
  'wordlist',
  'wordlists',
  'worklet',
  'xaid',
  'xbuf',
  'xfp',
  'xhr',
  'xmr',
  'Xmr',
  'xprivkey',
  'xprv',
  'Xprv',
  'xprvt',
  'xpub',
  'xpub2blake2b224',
  'xpubkey',
  'xpvt',
  'xrp',
  'Xrp',
  'Xudt',
  'Xxs',
  'xxxxxx',
  'ybuf',
  'yml',
  'zhihu',
  'zondax',
  'Zx4',
  'zxcvbn',
  "'aes",
  "'algosdk'",
  "'algosdk",
  "'axios'",
  "'injective'",
  "'mir",
  "'msg",
  "'nexatest",
  "'rbga",
  "'resize'",
  "'rsa",
  "'schnorr'",
  "'shelley'",
  "'utf",
  "'utf8'",
  "'utxo",
  "'withdrawable",
  "'xprv'",
  "'zhihu",
  "algo'",
  "bch'",
  "btc'",
  "cbc'",
  "cbor'",
  "cfx'",
  "evm'",
  "fil'",
  "kaspa'",
  "ltc'",
  "nexa'",
  "nqtsq5g50frur0vav60gupjlrr8cta8vyqufu7p9gskt92mz'",
  "nqtsq5g5l7rgf6mrvuhrjke8vsm4ng60q86vdycptqn79epv'",
  "nqtsq5g5llmjhut9fuzst4993zmk62m89rw2gztuvl376dp0'",
  "nqtsq5g5skxwlgtmsl99hj6tt8hmsa52cxft09um2md36p07'",
  "nqtsq5g5wud2fr7l32as0mfzms3hwnz7dxvsc2h8szatr5p8'",
  "sdk'",
  "stc'",
  "sui'",
  "ttf'",
  "xmr'",
  "xrp'",
  'sidepanel',
  'checkboxes',
  'Scroller',
  'scroller',
];

import type { DependencyList } from 'react';
import { useCallback, useEffect, useState } from 'react';

import { Keyboard, Platform } from 'react-native';
import { withTiming } from 'react-native-reanimated';

import platformEnv from '@onekeyhq/shared/src/platformEnv';

import type { EmitterSubscription, KeyboardEventListener } from 'react-native';

export function useIsKeyboardShown() {
  const [isKeyboardShown, setIsKeyboardShown] = useState(false);

  useEffect(() => {
    const handleKeyboardShow = () => setIsKeyboardShown(true);
    const handleKeyboardHide = () => setIsKeyboardShown(false);

    let subscriptions: EmitterSubscription[];

    if (Platform.OS === 'ios') {
      subscriptions = [
        Keyboard.addListener('keyboardWillShow', handleKeyboardShow),
        Keyboard.addListener('keyboardWillHide', handleKeyboardHide),
      ];
    } else {
      subscriptions = [
        Keyboard.addListener('keyboardDidShow', handleKeyboardShow),
        Keyboard.addListener('keyboardDidHide', handleKeyboardHide),
      ];
    }

    return () => {
      subscriptions.forEach((s) => s.remove());
    };
  }, []);

  return isKeyboardShown;
}

export const KEYBOARD_SHOW_EVENT_NAME = platformEnv.isNativeIOS
  ? 'keyboardWillShow'
  : 'keyboardDidShow';
export const KEYBOARD_HIDE_EVENT_NAME = platformEnv.isNativeIOS
  ? 'keyboardWillHide'
  : 'keyboardDidHide';

export function useKeyboardHeight() {
  const [keyboardHeight, setKeyboardHeight] = useState<number>(0);

  const handleKeyboardWillShow: KeyboardEventListener = useCallback((e) => {
    setKeyboardHeight(e.endCoordinates.height);
  }, []);
  // const handleKeyboardDidShow: KeyboardEventListener = useCallback((e) => {});
  const handleKeyboardWillHide: KeyboardEventListener = useCallback(() => {
    setKeyboardHeight(0);
  }, []);
  // const handleKeyboardDidHide: KeyboardEventListener = useCallback((e) => {});

  useEffect(() => {
    const subscriptions = [
      Keyboard.addListener(KEYBOARD_SHOW_EVENT_NAME, handleKeyboardWillShow),
      Keyboard.addListener(KEYBOARD_HIDE_EVENT_NAME, handleKeyboardWillHide),
    ];

    return () => {
      subscriptions.forEach((subscription) => subscription.remove());
    };
  }, [handleKeyboardWillHide, handleKeyboardWillShow]);

  return keyboardHeight;
}

const noop = () => undefined;
export const useKeyboardEvent = (
  {
    keyboardWillShow = noop,
    keyboardWillHide = noop,
  }: {
    keyboardWillShow?: KeyboardEventListener;
    keyboardWillHide?: KeyboardEventListener;
  },
  deps: DependencyList = [],
) => {
  useEffect(() => {
    const showSubscription = Keyboard.addListener(
      KEYBOARD_SHOW_EVENT_NAME,
      keyboardWillShow,
    );

    const hideSubscription = Keyboard.addListener(
      KEYBOARD_HIDE_EVENT_NAME,
      keyboardWillHide,
    );
    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, deps);
};

export const updateHeightWhenKeyboardShown = (height: number) =>
  withTiming(height, {
    duration: platformEnv.isNativeIOS ? 200 : 30,
  });

export const updateHeightWhenKeyboardHide = (height = 0) =>
  withTiming(height, {
    duration: 250,
  });

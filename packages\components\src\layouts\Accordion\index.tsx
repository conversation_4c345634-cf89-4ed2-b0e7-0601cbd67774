import type {
  AccordionContentProps,
  AccordionHeaderProps,
  AccordionItemProps,
  AccordionMultipleProps,
  AccordionSingleProps,
  AccordionTriggerProps,
} from 'tamagui';

export { Accordion } from 'tamagui';
export type IAccordionContentProps = AccordionContentProps;
export type IAccordionHeaderProps = AccordionHeaderProps;
export type IAccordionItemProps = AccordionItemProps;
export type IAccordionMultipleProps = AccordionMultipleProps;
export type IAccordionSingleProps = AccordionSingleProps;
export type IAccordionTriggerProps = AccordionTriggerProps;

{"name": "@onekeyhq/mobile", "version": "1.0.0", "main": "index.ts", "private": true, "scripts": {"android": "SENTRY_DISABLE_AUTO_UPLOAD=true ANDROID_CHANNEL=direct expo run:android --variant=ProdDebug", "android:device": "SENTRY_DISABLE_AUTO_UPLOAD=true ANDROID_CHANNEL=direct expo run:android --variant=ProdDebug --device", "android:huawei": "SENTRY_DISABLE_AUTO_UPLOAD=true ANDROID_CHANNEL=huawei expo run:android --variant=ProdDebug", "android:google": "SENTRY_DISABLE_AUTO_UPLOAD=true ANDROID_CHANNEL=google expo run:android --variant=GoogleDebug", "android:build": "cd android && ./gradlew assembleDebug", "ios": "SENTRY_DISABLE_AUTO_UPLOAD=true expo run:ios", "ios:device": "SENTRY_DISABLE_AUTO_UPLOAD=true expo run:ios --device", "ios:pod-install": "cd ios && pod install && cd ..", "native-bundle": "react-native start", "clean": "yarn clean:build", "clean:build": "rimraf ./dist && rimraf ./expo && rimraf .tamagui && rimraf ./node_modules/.cache && yarn android:clean", "android:clean": "./android/gradlew clean -p ./android", "android:dependencies": "./android/gradlew dependencies -p ./android", "native-bundle:analyze": "react-native-bundle-visualizer", "eas-build-post-install": "echo $KEY_SECRET | base64 -d > ../../node_modules/@onekeyfe/react-native-lite-card/keys/keys.secret", "_folderslint": "yarn folderslint"}, "devDependencies": {"@babel/core": "^7.20.0", "folderslint": "^1.2.0", "react-native-bundle-visualizer": "^3.1.3", "rimraf": "^3"}, "dependencies": {"@backpackapp-io/react-native-toast": "0.14.0", "@bufgix/react-native-secure-window": "0.1.1", "@expo-google-fonts/inter": "^0.2.3", "@formatjs/intl-getcanonicallocales": "^1.9.2", "@formatjs/intl-locale": "^2.4.47", "@formatjs/intl-pluralrules": "^4.3.3", "@notifee/react-native": "9.0.0", "@onekeyfe/react-native-animated-charts": "1.0.0", "@onekeyfe/react-native-ble-utils": "0.1.4", "@onekeyfe/react-native-lite-card": "1.0.14", "@onekeyfe/react-native-text-input": "0.2.9", "@onekeyhq/components": "*", "@onekeyhq/kit": "*", "@onekeyhq/shared": "*", "@privy-io/expo": "0.49.1", "@privy-io/expo-native-extensions": "0.0.4", "@react-native-async-storage/async-storage": "2.2.0", "@react-native-community/slider": "4.5.7", "@react-native-google-signin/google-signin": "^9.1.0", "@sentry/react-native": "6.16.1", "@shopify/flash-list": "2.0.2", "@types/react-native-canvas": "^0.1.13", "@walletconnect/modal-react-native": "1.1.0", "@walletconnect/react-native-compat": "2.11.2", "burnt": "0.12.2", "expo": "53.0.19", "expo-apple-authentication": "7.2.4", "expo-application": "6.1.4", "expo-blur": "14.1.5", "expo-clipboard": "7.1.4", "expo-constants": "17.1.6", "expo-crypto": "14.1.5", "expo-device": "7.1.4", "expo-font": "13.3.1", "expo-haptics": "14.1.4", "expo-image": "2.3.2", "expo-image-loader": "5.1.0", "expo-image-manipulator": "13.1.7", "expo-image-picker": "16.1.4", "expo-keep-awake": "14.1.4", "expo-linear-gradient": "14.1.5", "expo-linking": "7.1.5", "expo-local-authentication": "16.0.4", "expo-localization": "16.1.5", "expo-navigation-bar": "4.2.7", "expo-notifications": "0.31.3", "expo-screen-capture": "7.1.5", "expo-screen-orientation": "8.1.7", "expo-secure-store": "14.2.3", "expo-sharing": "13.1.5", "expo-splash-screen": "0.30.9", "expo-web-browser": "14.2.0", "https-browserify": "^1.0.0", "jcore-react-native": "https://github.com/OneKeyHQ/jcore-react-native.git#aa463b7296cf9e7095a3ba9965e97c1d3d03df56", "jpush-react-native": "https://github.com/OneKeyHQ/jpush-react-native.git#f10dfb5fe18fc042e6d4a82ec12f6b1d431adac7", "lottie-react": "^2.4.0", "lottie-react-native": "7.2.2", "path-browserify": "^1.0.1", "react": "19.0.0", "react-native": "0.79.5", "react-native-aes-crypto": "3.2.1", "react-native-ble-plx": "3.5.0", "react-native-camera-kit": "15.1.0", "react-native-canvas": "^0.1.39", "react-native-check-biometric-auth-changed": "0.1.1", "react-native-cloud-fs": "npm:@onekeyfe/react-native-cloud-fs@2.6.3", "react-native-collapsible-tab-view": "8.0.1", "react-native-crypto": "^2.2.0", "react-native-file-logger": "0.6.1", "react-native-fs": "npm:@dr.pogodin/react-native-fs@2.34.0", "react-native-gesture-handler": "2.27.1", "react-native-get-random-values": "1.9.0", "react-native-image-colors": "^2.5.0", "react-native-image-crop-picker": "0.50.0", "react-native-keyboard-controller": "1.17.5", "react-native-level-fs": "3.0.1", "react-native-mmkv": "3.2.0", "react-native-modal": "^13.0.1", "react-native-network-logger": "2.0.0", "react-native-pager-view": "6.8.1", "react-native-passkeys": "0.3.3", "react-native-performance-stats": "0.2.3", "react-native-permissions": "5.4.2", "react-native-purchases": "8.11.9", "react-native-qrcode-styled": "0.3.3", "react-native-reanimated": "3.18.0", "react-native-restart": "npm:react-native-restart-newarch@1.0.75", "react-native-safe-area-context": "5.4.1", "react-native-screens": "4.11.0", "react-native-svg": "15.12.0", "react-native-tcp-socket": "6.3.0", "react-native-video": "6.14.0", "react-native-view-shot": "4.0.3", "react-native-webview": "13.15.0", "react-native-webview-cleaner": "npm:@onekeyfe/react-native-webview-cleaner@1.0.0", "react-native-zip-archive": "7.0.2", "readable-stream": "^3.6.0", "realm": "12.14.2", "realm-flipper-plugin-device": "^1.1.0", "stream-http": "^3.2.0", "uuid": "^3.0.0"}, "workspaces": {"installConfig": {"installConfig.hoistingLimits": ["uuid"]}}, "expo": {"autolinking": {"android": {"exclude": ["@privy-io/expo-native-extensions"]}, "ios": {"exclude": ["@react-native-google-signin/google-signin"]}}}}